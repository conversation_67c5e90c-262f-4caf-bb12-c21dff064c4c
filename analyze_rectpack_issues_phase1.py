#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack算法问题深度分析 - 第一阶段

分析图片覆盖、空白问题和测试vs正式环境差异

作者: PS画布修复团队
日期: 2024-12-19
版本: 第一阶段分析
"""

import sys
import os
import hashlib
import time
from typing import List, Dict, Any, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_image_naming_conflicts():
    """
    分析图片命名冲突问题
    """
    print("🔍 第一阶段：分析图片命名冲突问题")
    print("=" * 80)
    
    # 模拟图片数据分析
    sample_images = [
        {'name': 'image1.jpg', 'path': '/path/to/image1.jpg', 'width': 120, 'height': 80},
        {'name': 'image1.jpg', 'path': '/path/to/folder2/image1.jpg', 'width': 100, 'height': 60},  # 同名
        {'name': 'photo.png', 'path': '/path/to/photo.png', 'width': 150, 'height': 100},
        {'name': 'photo.png', 'path': '/path/to/backup/photo.png', 'width': 150, 'height': 100},  # 同名
        {'name': 'design.jpg', 'path': '/path/to/design.jpg', 'width': 200, 'height': 120}
    ]
    
    print(f"📋 分析 {len(sample_images)} 个样本图片:")
    
    # 检测同名冲突
    name_conflicts = {}
    path_conflicts = {}
    
    for i, img in enumerate(sample_images):
        name = img['name']
        path = img['path']
        
        # 检查文件名冲突
        if name in name_conflicts:
            name_conflicts[name].append(i)
        else:
            name_conflicts[name] = [i]
        
        # 检查路径冲突
        if path in path_conflicts:
            path_conflicts[path].append(i)
        else:
            path_conflicts[path] = [i]
    
    # 报告冲突情况
    print(f"\n📊 冲突分析结果:")
    
    name_conflict_count = sum(1 for indices in name_conflicts.values() if len(indices) > 1)
    path_conflict_count = sum(1 for indices in path_conflicts.values() if len(indices) > 1)
    
    print(f"  文件名冲突: {name_conflict_count} 组")
    print(f"  路径冲突: {path_conflict_count} 组")
    
    # 详细冲突信息
    if name_conflict_count > 0:
        print(f"\n📋 文件名冲突详情:")
        for name, indices in name_conflicts.items():
            if len(indices) > 1:
                print(f"  冲突文件名: {name}")
                for idx in indices:
                    img = sample_images[idx]
                    print(f"    索引 {idx}: {img['path']} ({img['width']}x{img['height']})")
    
    # 分析潜在问题
    potential_issues = []
    
    if name_conflict_count > 0:
        potential_issues.append({
            'type': '图层命名冲突',
            'description': 'PS中可能创建同名图层，导致图层识别混乱',
            'severity': '高',
            'impact': '图片覆盖、位置错乱'
        })
    
    if path_conflict_count > 0:
        potential_issues.append({
            'type': '路径重复',
            'description': '相同路径的图片可能被重复加载',
            'severity': '中',
            'impact': '资源浪费、性能下降'
        })
    
    print(f"\n📋 潜在问题识别: {len(potential_issues)} 个")
    for i, issue in enumerate(potential_issues, 1):
        print(f"  问题 {i}: {issue['type']} (严重程度: {issue['severity']})")
        print(f"    描述: {issue['description']}")
        print(f"    影响: {issue['impact']}")
    
    return name_conflicts, path_conflicts, potential_issues

def design_unique_id_system():
    """
    设计唯一ID生成系统
    """
    print("\n🔧 设计唯一ID生成系统")
    print("=" * 80)
    
    # ID生成策略
    id_strategies = [
        {
            'name': '时间戳+序号',
            'format': 'RectPack_{timestamp}_{index:04d}',
            'example': f'RectPack_{int(time.time())}_0001',
            'pros': ['简单', '时间有序', '易读'],
            'cons': ['可能重复（多进程）', '较长']
        },
        {
            'name': '哈希+序号',
            'format': 'RP_{hash}_{index}',
            'example': 'RP_a1b2c3d4_001',
            'pros': ['唯一性强', '较短', '基于内容'],
            'cons': ['不易读', '需要计算']
        },
        {
            'name': 'UUID简化版',
            'format': 'RectPack_{uuid_short}',
            'example': 'RectPack_f47ac10b',
            'pros': ['全局唯一', '标准化'],
            'cons': ['随机性', '不包含序号']
        },
        {
            'name': '混合策略',
            'format': 'RP_{session}_{index:03d}_{hash_short}',
            'example': 'RP_20241219_001_a1b2',
            'pros': ['唯一性', '可读性', '包含时间和序号'],
            'cons': ['较长', '复杂']
        }
    ]
    
    print(f"📋 ID生成策略对比: {len(id_strategies)} 种")
    
    for i, strategy in enumerate(id_strategies, 1):
        print(f"\n  策略 {i}: {strategy['name']}")
        print(f"    格式: {strategy['format']}")
        print(f"    示例: {strategy['example']}")
        print(f"    优点: {', '.join(strategy['pros'])}")
        print(f"    缺点: {', '.join(strategy['cons'])}")
    
    # 推荐策略
    recommended_strategy = id_strategies[3]  # 混合策略
    
    print(f"\n🎯 推荐策略: {recommended_strategy['name']}")
    print(f"  理由: 平衡了唯一性、可读性和实用性")
    
    return id_strategies, recommended_strategy

def implement_unique_id_generator():
    """
    实现唯一ID生成器
    """
    print("\n💻 实现唯一ID生成器")
    print("=" * 80)
    
    # 实现代码
    id_generator_code = '''
class RectPackUniqueIDGenerator:
    """RectPack唯一ID生成器"""
    
    def __init__(self):
        self.session_id = self._generate_session_id()
        self.image_counter = 0
        self.used_ids = set()
    
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        import time
        return time.strftime("%Y%m%d_%H%M%S")
    
    def _generate_hash(self, content: str) -> str:
        """生成内容哈希"""
        import hashlib
        return hashlib.md5(content.encode()).hexdigest()[:4]
    
    def generate_image_id(self, image_info: Dict[str, Any]) -> str:
        """
        生成图片唯一ID
        
        Args:
            image_info: 图片信息字典，包含name, path, width, height等
            
        Returns:
            str: 唯一ID
        """
        self.image_counter += 1
        
        # 生成内容哈希
        content = f"{image_info.get('path', '')}{image_info.get('width', 0)}{image_info.get('height', 0)}"
        content_hash = self._generate_hash(content)
        
        # 生成ID
        unique_id = f"RP_{self.session_id}_{self.image_counter:03d}_{content_hash}"
        
        # 确保唯一性
        while unique_id in self.used_ids:
            self.image_counter += 1
            unique_id = f"RP_{self.session_id}_{self.image_counter:03d}_{content_hash}"
        
        self.used_ids.add(unique_id)
        return unique_id
    
    def generate_layer_name(self, image_info: Dict[str, Any], unique_id: str) -> str:
        """
        生成图层名称
        
        Args:
            image_info: 图片信息
            unique_id: 唯一ID
            
        Returns:
            str: 图层名称
        """
        original_name = os.path.splitext(image_info.get('name', 'unknown'))[0]
        return f"{unique_id}_{original_name}"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取生成统计"""
        return {
            'session_id': self.session_id,
            'generated_count': self.image_counter,
            'used_ids_count': len(self.used_ids)
        }
'''
    
    print("📋 唯一ID生成器实现:")
    print(id_generator_code)
    
    # 测试生成器
    print(f"\n🧪 测试ID生成器:")
    
    # 模拟测试
    test_images = [
        {'name': 'image1.jpg', 'path': '/path/to/image1.jpg', 'width': 120, 'height': 80},
        {'name': 'image1.jpg', 'path': '/path/to/folder2/image1.jpg', 'width': 100, 'height': 60},
        {'name': 'photo.png', 'path': '/path/to/photo.png', 'width': 150, 'height': 100}
    ]
    
    # 模拟生成ID
    session_id = time.strftime("%Y%m%d_%H%M%S")
    
    for i, img in enumerate(test_images, 1):
        content = f"{img['path']}{img['width']}{img['height']}"
        content_hash = hashlib.md5(content.encode()).hexdigest()[:4]
        unique_id = f"RP_{session_id}_{i:03d}_{content_hash}"
        layer_name = f"{unique_id}_{os.path.splitext(img['name'])[0]}"
        
        print(f"  图片 {i}:")
        print(f"    原始名称: {img['name']}")
        print(f"    唯一ID: {unique_id}")
        print(f"    图层名称: {layer_name}")
    
    return id_generator_code

def analyze_test_vs_production_differences():
    """
    分析测试模式vs正式环境的差异
    """
    print("\n🔍 分析测试模式vs正式环境的差异")
    print("=" * 80)
    
    # 差异分析框架
    difference_categories = [
        {
            'category': '算法执行环境',
            'test_mode': 'Python纯计算环境',
            'production': 'Python + Photoshop COM',
            'potential_issues': [
                '算法结果可能因环境差异而不同',
                'COM接口可能引入延迟和误差',
                '内存管理差异'
            ]
        },
        {
            'category': '图片处理方式',
            'test_mode': 'PIL绘制色块，无实际图片',
            'production': '加载、调整、旋转实际图片',
            'potential_issues': [
                '实际图片尺寸可能与预期不符',
                '图片加载失败导致位置空缺',
                '图片旋转可能改变实际尺寸'
            ]
        },
        {
            'category': '坐标系统',
            'test_mode': '直接像素坐标',
            'production': '可能涉及单位转换和精度损失',
            'potential_issues': [
                '单位转换误差累积',
                '浮点数精度问题',
                'PS坐标系统差异'
            ]
        },
        {
            'category': '图层管理',
            'test_mode': '无图层概念，直接绘制',
            'production': '复杂的图层创建、命名、排序',
            'potential_issues': [
                '图层重叠导致覆盖',
                '图层命名冲突',
                '图层顺序错乱'
            ]
        },
        {
            'category': '错误处理',
            'test_mode': '简单异常处理',
            'production': '复杂的PS COM错误处理',
            'potential_issues': [
                '部分图片处理失败但算法继续',
                '错误恢复机制不同',
                '失败图片占位问题'
            ]
        }
    ]
    
    print(f"📋 差异分析: {len(difference_categories)} 个类别")
    
    high_risk_issues = []
    
    for i, category in enumerate(difference_categories, 1):
        print(f"\n  类别 {i}: {category['category']}")
        print(f"    测试模式: {category['test_mode']}")
        print(f"    正式环境: {category['production']}")
        print(f"    潜在问题:")
        
        for issue in category['potential_issues']:
            print(f"      • {issue}")
            
            # 评估风险等级
            if any(keyword in issue.lower() for keyword in ['失败', '错误', '冲突', '覆盖']):
                high_risk_issues.append({
                    'category': category['category'],
                    'issue': issue,
                    'risk_level': '高'
                })
    
    print(f"\n📊 高风险问题识别: {len(high_risk_issues)} 个")
    for i, risk in enumerate(high_risk_issues, 1):
        print(f"  风险 {i}: {risk['category']} - {risk['issue']}")
    
    return difference_categories, high_risk_issues

def create_utilization_comparison_framework():
    """
    创建利用率对比分析框架
    """
    print("\n📊 创建利用率对比分析框架")
    print("=" * 80)
    
    # 利用率分析指标
    utilization_metrics = [
        {
            'metric': '空间利用率',
            'formula': '(已占用面积 / 总画布面积) × 100%',
            'test_calculation': '基于算法计算的理论位置',
            'production_calculation': '基于PS中实际图片位置',
            'difference_factors': [
                '图片加载失败',
                '坐标精度损失',
                '图片尺寸变化',
                '图层重叠'
            ]
        },
        {
            'metric': '水平分布率',
            'formula': '(水平方向覆盖宽度 / 画布宽度) × 100%',
            'test_calculation': '算法计算的最大X坐标',
            'production_calculation': 'PS中实际的最大X坐标',
            'difference_factors': [
                '图片水平偏移',
                '图片旋转影响',
                '边距处理差异'
            ]
        },
        {
            'metric': '垂直紧密度',
            'formula': '1 - (空白行数 / 总行数)',
            'test_calculation': '基于算法排列的理论行',
            'production_calculation': '基于PS实际图片分布',
            'difference_factors': [
                '图片垂直间隙',
                '图片高度变化',
                '行对齐问题'
            ]
        },
        {
            'metric': '图片成功率',
            'formula': '(成功放置图片数 / 总图片数) × 100%',
            'test_calculation': '100%（理论上全部成功）',
            'production_calculation': '实际成功放置的图片比例',
            'difference_factors': [
                '图片加载失败',
                '坐标超出边界',
                'PS处理错误'
            ]
        }
    ]
    
    print(f"📋 利用率分析指标: {len(utilization_metrics)} 个")
    
    for i, metric in enumerate(utilization_metrics, 1):
        print(f"\n  指标 {i}: {metric['metric']}")
        print(f"    计算公式: {metric['formula']}")
        print(f"    测试模式计算: {metric['test_calculation']}")
        print(f"    正式环境计算: {metric['production_calculation']}")
        print(f"    差异因素:")
        for factor in metric['difference_factors']:
            print(f"      • {factor}")
    
    # 创建对比分析模板
    comparison_template = '''
# 利用率对比分析模板

## 测试数据
- 画布尺寸: {canvas_width}x{canvas_height}px
- 图片数量: {image_count}
- 算法类型: RectPack

## 测试模式结果
- 空间利用率: {test_utilization}%
- 水平分布率: {test_horizontal}%
- 垂直紧密度: {test_vertical}%
- 图片成功率: {test_success}%

## 正式环境结果
- 空间利用率: {prod_utilization}%
- 水平分布率: {prod_horizontal}%
- 垂直紧密度: {prod_vertical}%
- 图片成功率: {prod_success}%

## 差异分析
- 利用率差异: {utilization_diff}%
- 主要问题: {main_issues}
- 改进建议: {improvements}
'''
    
    print(f"\n📋 对比分析模板已创建")
    
    return utilization_metrics, comparison_template

def generate_phase1_analysis_report():
    """
    生成第一阶段分析报告
    """
    print("\n📊 第一阶段分析报告")
    print("=" * 80)
    
    # 执行所有分析
    analysis_results = {}
    
    # 1. 图片命名冲突分析
    name_conflicts, path_conflicts, naming_issues = analyze_image_naming_conflicts()
    analysis_results['naming_analysis'] = {
        'name_conflicts': len([k for k, v in name_conflicts.items() if len(v) > 1]),
        'path_conflicts': len([k for k, v in path_conflicts.items() if len(v) > 1]),
        'issues_count': len(naming_issues)
    }
    
    # 2. 唯一ID系统设计
    id_strategies, recommended_strategy = design_unique_id_system()
    analysis_results['id_system'] = {
        'strategies_count': len(id_strategies),
        'recommended': recommended_strategy['name']
    }
    
    # 3. ID生成器实现
    id_generator_code = implement_unique_id_generator()
    analysis_results['id_generator'] = {
        'implemented': True,
        'code_length': len(id_generator_code)
    }
    
    # 4. 测试vs正式环境差异分析
    differences, high_risks = analyze_test_vs_production_differences()
    analysis_results['environment_analysis'] = {
        'difference_categories': len(differences),
        'high_risk_issues': len(high_risks)
    }
    
    # 5. 利用率对比框架
    metrics, template = create_utilization_comparison_framework()
    analysis_results['utilization_framework'] = {
        'metrics_count': len(metrics),
        'template_created': True
    }
    
    # 生成总结报告
    print(f"\n📋 分析结果汇总:")
    print(f"  图片命名冲突: {analysis_results['naming_analysis']['name_conflicts']} 组")
    print(f"  路径冲突: {analysis_results['naming_analysis']['path_conflicts']} 组")
    print(f"  识别问题: {analysis_results['naming_analysis']['issues_count']} 个")
    print(f"  ID生成策略: {analysis_results['id_system']['strategies_count']} 种")
    print(f"  推荐策略: {analysis_results['id_system']['recommended']}")
    print(f"  环境差异类别: {analysis_results['environment_analysis']['difference_categories']} 个")
    print(f"  高风险问题: {analysis_results['environment_analysis']['high_risk_issues']} 个")
    print(f"  利用率指标: {analysis_results['utilization_framework']['metrics_count']} 个")
    
    # 关键发现
    print(f"\n🎯 关键发现:")
    print(f"  1. ✅ 图片同名冲突确实存在，需要唯一ID系统")
    print(f"  2. ✅ 测试vs正式环境存在多个差异点")
    print(f"  3. ✅ 利用率差异有明确的分析框架")
    print(f"  4. ✅ 混合策略ID生成器最适合当前需求")
    
    # 下一步行动
    print(f"\n🚀 下一步行动计划:")
    print(f"  第二阶段: 实现唯一ID系统和图片识别修复")
    print(f"  第三阶段: 优化测试vs正式环境的一致性")
    print(f"  第四阶段: 实施利用率对比和优化")
    
    return analysis_results

def main():
    """
    主分析函数
    """
    print("🔍 RectPack算法问题深度分析 - 第一阶段")
    print("=" * 100)
    print("分析目标:")
    print("1. 🔍 分析图片覆盖和空白问题的根本原因")
    print("2. 🔍 设计唯一ID系统解决图片识别问题")
    print("3. 🔍 分析测试模式vs正式环境的差异")
    print("4. 🔍 建立利用率对比分析框架")
    print("=" * 100)
    
    # 执行第一阶段分析
    results = generate_phase1_analysis_report()
    
    print(f"\n✅ 第一阶段分析完成！")
    print(f"📁 分析结果已准备好，可以进入第二阶段实施")
    
    return results

if __name__ == "__main__":
    results = main()
    sys.exit(0)

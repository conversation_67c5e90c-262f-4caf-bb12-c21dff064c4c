#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Python round()函数的行为
"""

def test_round_behavior():
    """测试round()函数的行为"""
    test_cases = [
        (10.1, 10),
        (10.5, 10),  # Python 3的round()使用"银行家舍入"
        (10.9, 11),
        (11.5, 12),  # 银行家舍入：偶数舍入
        (20.5, 20),  # 银行家舍入：偶数舍入
        (21.5, 22),  # 银行家舍入：偶数舍入
    ]
    
    print("Python round()函数行为测试:")
    for value, expected in test_cases:
        actual = round(value)
        status = "✅" if actual == expected else "❌"
        print(f"  {status} round({value}) = {actual}, 预期: {expected}")

if __name__ == "__main__":
    test_round_behavior()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Tetris算法参数移除

验证从配置管理器中成功移除了所有Tetris算法相关参数

作者: RectPack参数设置团队
日期: 2024-12-19
版本: Tetris移除验证
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_tetris_parameters_removal():
    """测试Tetris算法参数移除"""
    print("🧪 测试Tetris算法参数移除")
    print("=" * 80)
    
    try:
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器实例
        config_manager = ConfigManagerDuckDB()
        
        print(f"📋 Tetris算法参数移除验证:")
        
        # 定义应该被移除的Tetris算法参数
        removed_tetris_params = [
            # 图片分类参数
            'class_a_threshold',
            'class_b_error_range',
            
            # 旋转决策参数
            'class_a_rotation_threshold',
            'class_b_rotation_threshold',
            'class_c_rotation_threshold',
            'extreme_ratio_threshold',
            'extreme_ratio_utilization',
            
            # 行空隙填充参数
            'row_utilization_threshold',
            'class_c_gap_error_range',
            'enable_row_gap_filling',
            
            # C类俄罗斯方块算法高级参数
            'c_min_utilization_improvement',
            'c_high_utilization_threshold',
            'c_extreme_ratio_min',
            'c_horizontal_priority',
            'c_gap_filling_priority',
            'c_rotation_priority',
            
            # 画布利用率迭代参数
            'is_check_canvas',
            'canvas_iteration_count',
            'canvas_iteration_time'
        ]
        
        # 检查这些参数是否还存在于默认配置中
        existing_params = []
        for param in removed_tetris_params:
            try:
                value = config_manager.get(param)
                if value is not None:
                    existing_params.append(param)
            except:
                pass  # 参数不存在，这是预期的
        
        if existing_params:
            print(f"  ❌ 以下Tetris参数仍然存在: {existing_params}")
            return False
        else:
            print(f"  ✅ 所有Tetris参数已成功移除 ({len(removed_tetris_params)} 个)")
        
        # 验证移除的方法不再存在
        removed_methods = [
            'get_algorithm_settings',
            'sync_c_class_algorithm_params',
            'get_c_class_algorithm_params',
            'get_canvas_iteration_params',
            'sync_canvas_iteration_params'
        ]
        
        existing_methods = []
        for method_name in removed_methods:
            if hasattr(config_manager, method_name):
                existing_methods.append(method_name)
        
        if existing_methods:
            print(f"  ❌ 以下Tetris方法仍然存在: {existing_methods}")
            return False
        else:
            print(f"  ✅ 所有Tetris方法已成功移除 ({len(removed_methods)} 个)")
        
        return True
        
    except Exception as e:
        print(f"❌ Tetris算法参数移除验证失败: {str(e)}")
        return False

def test_rectpack_parameters_still_exist():
    """测试RectPack算法参数仍然存在"""
    print("\n🧪 测试RectPack算法参数仍然存在")
    print("=" * 80)
    
    try:
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器实例
        config_manager = ConfigManagerDuckDB()
        
        print(f"📋 RectPack算法参数存在验证:")
        
        # 获取RectPack设置
        rectpack_settings = config_manager.get_rectpack_settings()
        
        # 验证所有必需的RectPack参数
        required_rectpack_params = [
            # 基础参数
            'use_rectpack_algorithm',
            'rectpack_rotation_enabled',
            'rectpack_sort_strategy',
            'rectpack_pack_algorithm',
            
            # 高级参数
            'rectpack_bin_selection_strategy',
            'rectpack_split_heuristic',
            'rectpack_free_rect_choice',
            
            # 优化参数
            'rectpack_enable_optimization',
            'rectpack_optimization_iterations',
            'rectpack_min_utilization_threshold',
            'rectpack_rotation_penalty',
            'rectpack_aspect_ratio_preference',
            
            # 性能参数
            'rectpack_max_processing_time',
            'rectpack_batch_size',
            'rectpack_memory_limit_mb',
            'rectpack_enable_parallel',
            
            # 调试参数
            'rectpack_debug_mode',
            'rectpack_log_level',
            'rectpack_save_intermediate_results',
            'rectpack_visualization_enabled'
        ]
        
        missing_rectpack_params = []
        for param in required_rectpack_params:
            if param not in rectpack_settings:
                missing_rectpack_params.append(param)
        
        if missing_rectpack_params:
            print(f"  ❌ 缺少RectPack参数: {missing_rectpack_params}")
            return False
        else:
            print(f"  ✅ 所有RectPack参数都存在 ({len(required_rectpack_params)} 个)")
        
        # 验证RectPack方法仍然存在
        required_rectpack_methods = [
            'get_rectpack_settings',
            'set_rectpack_settings',
            'sync_rectpack_settings'
        ]
        
        missing_rectpack_methods = []
        for method_name in required_rectpack_methods:
            if not hasattr(config_manager, method_name):
                missing_rectpack_methods.append(method_name)
        
        if missing_rectpack_methods:
            print(f"  ❌ 缺少RectPack方法: {missing_rectpack_methods}")
            return False
        else:
            print(f"  ✅ 所有RectPack方法都存在 ({len(required_rectpack_methods)} 个)")
        
        return True
        
    except Exception as e:
        print(f"❌ RectPack算法参数存在验证失败: {str(e)}")
        return False

def test_config_file_integrity():
    """测试配置文件完整性"""
    print("\n🧪 测试配置文件完整性")
    print("=" * 80)
    
    try:
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器实例
        config_manager = ConfigManagerDuckDB()
        
        print(f"📋 配置文件完整性验证:")
        
        # 测试基本配置读取
        basic_configs = [
            'max_height_cm',
            'ppi',
            'image_spacing_cm',
            'horizontal_expansion_cm',
            'use_photoshop',
            'is_test_mode',
            'is_db_scan_fast'
        ]
        
        missing_basic_configs = []
        for config in basic_configs:
            try:
                value = config_manager.get(config)
                if value is None:
                    missing_basic_configs.append(config)
            except:
                missing_basic_configs.append(config)
        
        if missing_basic_configs:
            print(f"  ❌ 缺少基本配置: {missing_basic_configs}")
            return False
        else:
            print(f"  ✅ 所有基本配置都存在 ({len(basic_configs)} 个)")
        
        # 测试配置设置和获取
        test_key = 'test_removal_verification'
        test_value = 'tetris_removed_successfully'
        
        # 设置测试值
        set_success = config_manager.set(test_key, test_value)
        if not set_success:
            print(f"  ❌ 配置设置失败")
            return False
        
        # 获取测试值
        retrieved_value = config_manager.get(test_key)
        if retrieved_value != test_value:
            print(f"  ❌ 配置获取失败: 期望 {test_value}, 实际 {retrieved_value}")
            return False
        
        print(f"  ✅ 配置读写功能正常")
        
        # 清理测试数据
        config_manager.set(test_key, None)
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件完整性验证失败: {str(e)}")
        return False

def generate_tetris_removal_test_report():
    """生成Tetris算法参数移除测试报告"""
    print("\n📊 Tetris算法参数移除测试报告")
    print("=" * 80)
    
    # 执行所有测试
    test_results = {
        'tetris_parameters_removal': test_tetris_parameters_removal(),
        'rectpack_parameters_exist': test_rectpack_parameters_still_exist(),
        'config_file_integrity': test_config_file_integrity()
    }
    
    # 统计结果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    print(f"\n📋 测试结果汇总:")
    print(f"  Tetris参数移除: {'✅ 通过' if test_results['tetris_parameters_removal'] else '❌ 失败'}")
    print(f"  RectPack参数存在: {'✅ 通过' if test_results['rectpack_parameters_exist'] else '❌ 失败'}")
    print(f"  配置文件完整性: {'✅ 通过' if test_results['config_file_integrity'] else '❌ 失败'}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print(f"🎉 Tetris算法参数移除测试全部通过！")
        print(f"✅ Tetris算法参数已完全移除")
        
        # 移除效果总结
        print(f"\n🎯 移除效果总结:")
        print(f"  1. ✅ 移除了21个Tetris算法参数")
        print(f"  2. ✅ 移除了5个Tetris算法方法")
        print(f"  3. ✅ 保留了20个RectPack算法参数")
        print(f"  4. ✅ 保留了3个RectPack算法方法")
        print(f"  5. ✅ 配置文件完整性保持良好")
        
        print(f"\n🚀 清理效果:")
        print(f"  • 代码更简洁：移除了过时的Tetris算法支持")
        print(f"  • 参数更专注：只保留RectPack算法相关参数")
        print(f"  • 维护更容易：减少了代码复杂度")
        print(f"  • 功能更统一：统一使用RectPack算法")
        
    else:
        print(f"⚠️ Tetris算法参数移除测试存在问题，需要进一步清理")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🔧 Tetris算法参数移除验证测试")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证所有Tetris算法参数已被移除")
    print("2. 🔍 验证所有RectPack算法参数仍然存在")
    print("3. 🔍 验证配置文件完整性")
    print("=" * 100)
    
    # 执行测试
    success = generate_tetris_removal_test_report()
    
    if success:
        print(f"\n🎯 移除验证结果:")
        print(f"✅ Tetris算法参数已完全移除")
        print(f"🚀 现在系统只使用RectPack算法")
        print(f"📊 配置管理器更加简洁和专注")
    else:
        print(f"\n🔧 需要进一步清理:")
        print(f"1. 检查失败的测试用例")
        print(f"2. 完成剩余的Tetris参数移除")
        print(f"3. 重新验证移除效果")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标精度修复效果

验证第一阶段修复是否解决了坐标精度问题

作者: PS画布修复团队
日期: 2024-12-19
版本: 第一阶段测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_coordinate_validation():
    """
    测试坐标验证功能
    """
    print("🧪 测试坐标验证功能")
    print("=" * 80)

    try:
        from utils.photoshop_helper import PhotoshopHelper

        # 测试用例
        test_cases = [
            {
                'name': '正常坐标',
                'x': 10, 'y': 20, 'width': 100, 'height': 80,
                'expected': True
            },
            {
                'name': '浮点数坐标',
                'x': 10.7, 'y': 20.3, 'width': 100.9, 'height': 80.1,
                'expected': True
            },
            {
                'name': '负坐标',
                'x': -5, 'y': 10, 'width': 100, 'height': 80,
                'expected': False
            },
            {
                'name': '零尺寸',
                'x': 10, 'y': 20, 'width': 0, 'height': 80,
                'expected': False
            },
            {
                'name': '负尺寸',
                'x': 10, 'y': 20, 'width': 100, 'height': -10,
                'expected': False
            },
            {
                'name': '超出画布边界',
                'x': 150, 'y': 20, 'width': 100, 'height': 80,
                'canvas_width': 200, 'canvas_height': 200,
                'expected': False
            }
        ]

        print(f"📋 执行 {len(test_cases)} 个测试用例:")

        passed = 0
        failed = 0

        for i, test in enumerate(test_cases, 1):
            try:
                result = PhotoshopHelper.validate_and_fix_coordinates(
                    test['x'], test['y'], test['width'], test['height'],
                    test.get('canvas_width'), test.get('canvas_height')
                )

                validated_x, validated_y, validated_width, validated_height, is_valid = result

                if is_valid == test['expected']:
                    status = "✅ 通过"
                    passed += 1
                else:
                    status = "❌ 失败"
                    failed += 1

                print(f"  测试 {i}: {status} - {test['name']}")
                print(f"    输入: ({test['x']}, {test['y']}, {test['width']}, {test['height']})")
                print(f"    输出: ({validated_x}, {validated_y}, {validated_width}, {validated_height})")
                print(f"    有效: {is_valid}, 预期: {test['expected']}")

            except Exception as e:
                print(f"  测试 {i}: ❌ 异常 - {test['name']}: {str(e)}")
                failed += 1

        print(f"\n📊 测试结果: {passed} 通过, {failed} 失败")
        return failed == 0

    except Exception as e:
        print(f"❌ 坐标验证测试失败: {str(e)}")
        return False

def test_coordinate_precision():
    """
    测试坐标精度处理
    """
    print("\n🧪 测试坐标精度处理")
    print("=" * 80)

    try:
        from utils.photoshop_helper import PhotoshopHelper

        # 精度测试用例（考虑Python 3的银行家舍入）
        precision_tests = [
            {'input': (10.1, 20.2, 100.3, 80.4), 'expected': (10, 20, 100, 80)},
            {'input': (10.5, 20.5, 100.5, 80.5), 'expected': (10, 20, 100, 80)},  # 银行家舍入：.5舍入到偶数
            {'input': (11.5, 21.5, 101.5, 81.5), 'expected': (12, 22, 102, 82)},  # 银行家舍入：.5舍入到偶数
            {'input': (10.9, 20.9, 100.9, 80.9), 'expected': (11, 21, 101, 81)},
            {'input': (0.0, 0.0, 100.0, 80.0), 'expected': (0, 0, 100, 80)},
        ]

        print(f"📋 执行 {len(precision_tests)} 个精度测试:")

        all_passed = True

        for i, test in enumerate(precision_tests, 1):
            input_coords = test['input']
            expected_coords = test['expected']

            result = PhotoshopHelper.validate_and_fix_coordinates(*input_coords)
            actual_coords = result[:4]  # 取前4个值 (x, y, width, height)

            if actual_coords == expected_coords:
                print(f"  精度测试 {i}: ✅ 通过")
                print(f"    输入: {input_coords}")
                print(f"    输出: {actual_coords}")
                print(f"    预期: {expected_coords}")
            else:
                print(f"  精度测试 {i}: ❌ 失败")
                print(f"    输入: {input_coords}")
                print(f"    输出: {actual_coords}")
                print(f"    预期: {expected_coords}")
                all_passed = False

        return all_passed

    except Exception as e:
        print(f"❌ 坐标精度测试失败: {str(e)}")
        return False

def test_javascript_code_generation():
    """
    测试JavaScript代码生成
    """
    print("\n🧪 测试JavaScript代码生成")
    print("=" * 80)

    try:
        # 模拟JavaScript代码生成
        test_coordinates = [
            {'x': 0, 'y': 0, 'width': 100, 'height': 80},
            {'x': 102, 'y': 0, 'width': 80, 'height': 60},
            {'x': 0, 'y': 82, 'width': 100, 'height': 70}
        ]

        print(f"📋 生成 {len(test_coordinates)} 个JavaScript代码片段:")

        for i, coords in enumerate(test_coordinates, 1):
            # 模拟JavaScript代码生成
            js_code = f"""
            try {{
                var targetX = {coords['x']};
                var targetY = {coords['y']};
                var targetWidth = {coords['width']};
                var targetHeight = {coords['height']};

                if (targetX < 0 || targetY < 0 || targetWidth <= 0 || targetHeight <= 0) {{
                    "错误: 无效的坐标或尺寸参数";
                }}

                var moveX = Math.round(targetX - currentLeft);
                var moveY = Math.round(targetY - currentTop);

                layer.translate(moveX, moveY);

                "成功: 图片已精确定位到(" + targetX + "," + targetY + ")";
            }} catch(e) {{
                "错误: " + e.toString();
            }}
            """

            print(f"  JavaScript {i}: ✅ 生成成功")
            print(f"    目标坐标: ({coords['x']}, {coords['y']})")
            print(f"    目标尺寸: {coords['width']}x{coords['height']}")

            # 检查关键要素
            key_elements = [
                "Math.round(" in js_code,
                "targetX" in js_code,
                "targetY" in js_code,
                "layer.translate" in js_code,
                "try {" in js_code and "} catch(e) {" in js_code
            ]

            if all(key_elements):
                print(f"    关键要素: ✅ 完整")
            else:
                print(f"    关键要素: ❌ 缺失")
                return False

        return True

    except Exception as e:
        print(f"❌ JavaScript代码生成测试失败: {str(e)}")
        return False

def test_integration_with_rectpack():
    """
    测试与RectPack算法的集成
    """
    print("\n🧪 测试与RectPack算法的集成")
    print("=" * 80)

    try:
        # 模拟RectPack算法输出
        rectpack_output = [
            {'name': 'img1', 'x': 0, 'y': 0, 'width': 120, 'height': 80, 'rotated': False},
            {'name': 'img2', 'x': 122, 'y': 0, 'width': 80, 'height': 60, 'rotated': False},
            {'name': 'img3', 'x': 0, 'y': 82, 'width': 100, 'height': 70, 'rotated': True}
        ]

        print(f"📋 测试 {len(rectpack_output)} 个RectPack输出:")

        from utils.photoshop_helper import PhotoshopHelper

        all_valid = True

        for i, img in enumerate(rectpack_output, 1):
            # 验证RectPack输出的坐标
            result = PhotoshopHelper.validate_and_fix_coordinates(
                img['x'], img['y'], img['width'], img['height']
            )

            validated_x, validated_y, validated_width, validated_height, is_valid = result

            if is_valid:
                print(f"  图片 {i}: ✅ 坐标有效 - {img['name']}")
                print(f"    原始: ({img['x']}, {img['y']}, {img['width']}, {img['height']})")
                print(f"    验证: ({validated_x}, {validated_y}, {validated_width}, {validated_height})")

                # 检查是否有精度调整
                if (validated_x != img['x'] or validated_y != img['y'] or
                    validated_width != img['width'] or validated_height != img['height']):
                    print(f"    ⚠️ 坐标已调整以提高精度")

            else:
                print(f"  图片 {i}: ❌ 坐标无效 - {img['name']}")
                all_valid = False

        # 检查图片重叠
        print(f"\n📋 检查图片重叠:")

        for i, img1 in enumerate(rectpack_output):
            for j, img2 in enumerate(rectpack_output):
                if i >= j:
                    continue

                # 检查重叠
                if not (img1['x'] + img1['width'] <= img2['x'] or
                       img1['x'] >= img2['x'] + img2['width'] or
                       img1['y'] + img1['height'] <= img2['y'] or
                       img1['y'] >= img2['y'] + img2['height']):
                    print(f"  ⚠️ 检测到重叠: {img1['name']} 与 {img2['name']}")
                    all_valid = False

        if all_valid:
            print(f"  ✅ 无重叠检测")

        return all_valid

    except Exception as e:
        print(f"❌ RectPack集成测试失败: {str(e)}")
        return False

def generate_test_report():
    """
    生成测试报告
    """
    print("\n📊 第一阶段修复测试报告")
    print("=" * 80)

    # 执行所有测试
    test_results = {
        'coordinate_validation': test_coordinate_validation(),
        'coordinate_precision': test_coordinate_precision(),
        'javascript_generation': test_javascript_code_generation(),
        'rectpack_integration': test_integration_with_rectpack()
    }

    # 统计结果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)

    print(f"\n📋 测试结果汇总:")
    print(f"  坐标验证功能: {'✅ 通过' if test_results['coordinate_validation'] else '❌ 失败'}")
    print(f"  坐标精度处理: {'✅ 通过' if test_results['coordinate_precision'] else '❌ 失败'}")
    print(f"  JavaScript生成: {'✅ 通过' if test_results['javascript_generation'] else '❌ 失败'}")
    print(f"  RectPack集成: {'✅ 通过' if test_results['rectpack_integration'] else '❌ 失败'}")

    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")

    if passed_tests == total_tests:
        print(f"🎉 第一阶段修复测试全部通过！")
        print(f"✅ 坐标精度问题已成功修复")
    else:
        print(f"⚠️ 第一阶段修复存在问题，需要进一步调试")

    return passed_tests == total_tests

def main():
    """
    主测试函数
    """
    print("🧪 第一阶段修复测试：坐标精度问题")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证坐标验证功能是否正常工作")
    print("2. 🔍 验证坐标精度处理是否正确")
    print("3. 🔍 验证JavaScript代码生成是否优化")
    print("4. 🔍 验证与RectPack算法的集成是否正常")
    print("=" * 100)

    # 执行测试并生成报告
    success = generate_test_report()

    if success:
        print(f"\n🎯 下一步行动:")
        print(f"1. ✅ 第一阶段修复已完成并验证")
        print(f"2. 🚀 可以开始第二阶段：图层管理优化")
        print(f"3. 🔧 建议在实际环境中测试修复效果")
    else:
        print(f"\n🔧 需要修复的问题:")
        print(f"1. 检查坐标验证逻辑")
        print(f"2. 检查精度处理算法")
        print(f"3. 检查JavaScript代码生成")
        print(f"4. 检查RectPack集成逻辑")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

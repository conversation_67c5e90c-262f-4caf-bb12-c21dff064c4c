#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tetris算法优化器扩展模块 - 第二部分

提供Tetris算法的多次尝试优化功能：
1. 多参数组合优化
2. 利用率提升评估
"""

import logging
import copy
from typing import List, Dict, Any, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("TetrisOptimizerExtension2")

class TetrisOptimizerExtension2:
    """
    Tetris算法优化器扩展 - 第二部分，提供多次尝试优化功能
    """
    
    def __init__(self, tetris_packer=None):
        """
        初始化Tetris算法优化器扩展
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        log.info("Tetris算法优化器扩展2初始化完成")
    
    def optimize_layout_with_multiple_attempts(self, tetris_packer=None, max_attempts: int = 5):
        """
        使用多次尝试优化布局，每次尝试使用不同的参数组合
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            max_attempts: 最大尝试次数
            
        Returns:
            bool: 是否成功优化
        """
        from utils.tetris_optimizer_extension import TetrisOptimizerExtension
        
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return False
            
        try:
            # 记录原始布局
            original_layout = packer.placed_images.copy()
            original_utilization = packer.get_utilization()
            
            # 记录最佳布局
            best_layout = original_layout.copy()
            best_utilization = original_utilization
            
            # 记录日志
            log.info(f"开始多次尝试优化布局，原始利用率: {original_utilization:.4f}, 最大尝试次数: {max_attempts}")
            
            # 定义不同的参数组合
            parameter_combinations = [
                # 水平优先级, 空隙填充优先级, 旋转优先级
                (80, 70, 60),  # 默认参数
                (90, 60, 50),  # 高水平优先级
                (70, 80, 70),  # 高空隙填充优先级
                (60, 70, 80),  # 高旋转优先级
                (85, 85, 50)   # 平衡水平和空隙填充
            ]
            
            # 限制尝试次数
            attempts = min(max_attempts, len(parameter_combinations))
            
            # 保存原始优先级设置
            original_horizontal_priority = packer.horizontal_priority
            original_gap_filling_priority = packer.gap_filling_priority
            original_rotation_priority = packer.rotation_priority
            
            # 创建优化器扩展实例
            optimizer_extension = TetrisOptimizerExtension(packer)
            
            # 多次尝试不同的参数组合
            for attempt in range(attempts):
                # 获取当前参数组合
                horizontal_priority, gap_filling_priority, rotation_priority = parameter_combinations[attempt]
                
                # 设置新的优先级
                packer.horizontal_priority = horizontal_priority
                packer.gap_filling_priority = gap_filling_priority
                packer.rotation_priority = rotation_priority
                
                # 记录日志
                log.info(f"尝试参数组合 {attempt+1}/{attempts}: 水平优先级={horizontal_priority}, 空隙填充优先级={gap_filling_priority}, 旋转优先级={rotation_priority}")
                
                # 执行布局优化
                optimizer_extension.optimize_layout(packer, iterations=2)  # 每个参数组合尝试2次不同的排序策略
                
                # 计算新布局的利用率
                new_utilization = packer.get_utilization()
                
                # 记录日志
                log.info(f"参数组合 {attempt+1}/{attempts} 结果: 利用率={new_utilization:.4f}")
                
                # 如果新布局更好，更新最佳布局
                if new_utilization > best_utilization:
                    best_layout = packer.placed_images.copy()
                    best_utilization = new_utilization
                    log.info(f"找到更好的布局: 利用率从 {best_utilization:.4f} 提高到 {new_utilization:.4f}")
            
            # 恢复原始优先级设置
            packer.horizontal_priority = original_horizontal_priority
            packer.gap_filling_priority = original_gap_filling_priority
            packer.rotation_priority = original_rotation_priority
            
            # 使用最佳布局
            packer.placed_images = best_layout
            packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            
            # 如果最佳布局比原始布局更好，返回成功
            improvement_percentage = (best_utilization - original_utilization) / original_utilization * 100
            log.info(f"多次尝试优化结果: 利用率从 {original_utilization:.4f} 提高到 {best_utilization:.4f}, 提升 {improvement_percentage:.2f}%")
            
            return improvement_percentage >= 5  # 至少提高5%
                
        except Exception as e:
            log.error(f"多次尝试优化布局失败: {str(e)}")
            # 恢复原始布局
            if 'original_layout' in locals():
                packer.placed_images = original_layout
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RectPackArranger参数集成

验证RectPackArranger是否正确使用新的RectPack参数系统

作者: RectPack参数设置团队
日期: 2024-12-19
版本: 参数集成测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_parameter_loading():
    """测试参数加载功能"""
    print("🧪 测试RectPackArranger参数加载")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器
        config_manager = ConfigManagerDuckDB()
        
        # 获取RectPack设置
        rectpack_settings = config_manager.get_rectpack_settings()
        print(f"📋 配置管理器RectPack设置:")
        for key, value in rectpack_settings.items():
            print(f"  {key}: {value}")
        
        # 创建RectPackArranger实例
        arranger = RectPackArranger(
            container_width=200,
            max_height=300,
            image_spacing=1
        )
        
        print(f"\n📋 RectPackArranger参数验证:")
        
        # 验证算法参数
        print(f"  算法参数:")
        print(f"    旋转启用: {arranger.rotation_enabled}")
        print(f"    排序策略: {arranger.sort_key}")
        print(f"    装箱算法: {arranger.pack_algo}")
        print(f"    Bin选择策略: {getattr(arranger, 'bin_selection_strategy', '未设置')}")
        print(f"    分割启发式: {getattr(arranger, 'split_heuristic', '未设置')}")
        print(f"    自由矩形选择: {getattr(arranger, 'free_rect_choice', '未设置')}")
        
        # 验证性能参数
        print(f"  性能参数:")
        print(f"    最大处理时间: {getattr(arranger, 'max_processing_time', '未设置')}秒")
        print(f"    批处理大小: {getattr(arranger, 'batch_size', '未设置')}")
        print(f"    内存限制: {getattr(arranger, 'memory_limit_mb', '未设置')}MB")
        print(f"    并行处理: {getattr(arranger, 'enable_parallel', '未设置')}")
        
        # 验证调试参数
        print(f"  调试参数:")
        print(f"    调试模式: {getattr(arranger, 'debug_mode', '未设置')}")
        print(f"    日志级别: {getattr(arranger, 'log_level', '未设置')}")
        print(f"    保存中间结果: {getattr(arranger, 'save_intermediate_results', '未设置')}")
        print(f"    可视化启用: {getattr(arranger, 'visualization_enabled', '未设置')}")
        
        # 验证优化参数
        print(f"  优化参数:")
        print(f"    启用优化: {getattr(arranger, 'enable_optimization', '未设置')}")
        print(f"    优化迭代次数: {getattr(arranger, 'optimization_iterations', '未设置')}")
        print(f"    最小利用率阈值: {getattr(arranger, 'min_utilization_threshold', '未设置')}%")
        print(f"    旋转惩罚: {getattr(arranger, 'rotation_penalty', '未设置')}")
        print(f"    宽高比偏好: {getattr(arranger, 'aspect_ratio_preference', '未设置')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数加载测试失败: {str(e)}")
        return False

def test_parameter_reload():
    """测试参数重新加载功能"""
    print("\n🧪 测试参数重新加载功能")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器和arranger
        config_manager = ConfigManagerDuckDB()
        arranger = RectPackArranger(
            container_width=200,
            max_height=300,
            image_spacing=1
        )
        
        # 记录原始参数
        original_rotation = arranger.rotation_enabled
        original_sort_key = arranger.sort_key
        
        print(f"📋 原始参数:")
        print(f"  旋转启用: {original_rotation}")
        print(f"  排序策略: {original_sort_key}")
        
        # 修改配置
        config_manager.set('rectpack_rotation_enabled', not original_rotation)
        config_manager.set('rectpack_sort_strategy', 1 if original_sort_key == 0 else 0)
        
        # 重新加载参数
        arranger.reload_parameters()
        
        print(f"\n📋 重新加载后的参数:")
        print(f"  旋转启用: {arranger.rotation_enabled}")
        print(f"  排序策略: {arranger.sort_key}")
        
        # 验证参数是否已更新
        if arranger.rotation_enabled != original_rotation:
            print(f"  ✅ 旋转参数已更新")
        else:
            print(f"  ❌ 旋转参数未更新")
            return False
        
        if arranger.sort_key != original_sort_key:
            print(f"  ✅ 排序策略已更新")
        else:
            print(f"  ❌ 排序策略未更新")
            return False
        
        # 恢复原始设置
        config_manager.set('rectpack_rotation_enabled', original_rotation)
        config_manager.set('rectpack_sort_strategy', original_sort_key)
        
        return True
        
    except Exception as e:
        print(f"❌ 参数重新加载测试失败: {str(e)}")
        return False

def test_current_parameters_method():
    """测试获取当前参数方法"""
    print("\n🧪 测试获取当前参数方法")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        
        # 创建arranger
        arranger = RectPackArranger(
            container_width=200,
            max_height=300,
            image_spacing=1
        )
        
        # 获取当前参数
        current_params = arranger.get_current_parameters()
        
        print(f"📋 当前参数字典:")
        for category in ['算法参数', '性能参数', '调试参数', '优化参数']:
            print(f"  {category}:")
        
        # 验证参数完整性
        required_params = [
            'rotation_enabled', 'sort_key', 'pack_algo',
            'bin_selection_strategy', 'split_heuristic', 'free_rect_choice',
            'max_processing_time', 'batch_size', 'memory_limit_mb', 'enable_parallel',
            'debug_mode', 'log_level', 'save_intermediate_results', 'visualization_enabled',
            'enable_optimization', 'optimization_iterations', 'min_utilization_threshold',
            'rotation_penalty', 'aspect_ratio_preference'
        ]
        
        missing_params = []
        for param in required_params:
            if param not in current_params:
                missing_params.append(param)
            else:
                print(f"    {param}: {current_params[param]}")
        
        if missing_params:
            print(f"  ❌ 缺少参数: {missing_params}")
            return False
        else:
            print(f"  ✅ 所有参数都存在 ({len(required_params)} 个)")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取当前参数测试失败: {str(e)}")
        return False

def test_optimization_with_parameters():
    """测试使用新参数的优化功能"""
    print("\n🧪 测试使用新参数的优化功能")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器
        config_manager = ConfigManagerDuckDB()
        
        # 设置优化参数
        config_manager.set('rectpack_enable_optimization', True)
        config_manager.set('rectpack_optimization_iterations', 3)
        config_manager.set('rectpack_min_utilization_threshold', 80.0)
        
        # 创建arranger
        arranger = RectPackArranger(
            container_width=200,
            max_height=300,
            image_spacing=1
        )
        
        print(f"📋 优化参数验证:")
        print(f"  启用优化: {getattr(arranger, 'enable_optimization', False)}")
        print(f"  优化迭代次数: {getattr(arranger, 'optimization_iterations', 0)}")
        print(f"  最小利用率阈值: {getattr(arranger, 'min_utilization_threshold', 0)}%")
        
        # 添加一些测试图片
        test_images = [
            {'width': 50, 'height': 30, 'name': 'test1'},
            {'width': 40, 'height': 40, 'name': 'test2'},
            {'width': 60, 'height': 25, 'name': 'test3'}
        ]
        
        # 放置图片
        placed_count = 0
        for img in test_images:
            x, y, placed = arranger.place_image(img['width'], img['height'], img)
            if placed:
                placed_count += 1
                print(f"  ✅ 图片 {img['name']} 放置在 ({x}, {y})")
            else:
                print(f"  ❌ 图片 {img['name']} 放置失败")
        
        if placed_count > 0:
            # 测试优化功能
            print(f"\n📋 测试优化功能:")
            
            # 获取优化前的状态
            stats_before = arranger.get_layout_info()
            print(f"  优化前利用率: {stats_before.get('utilization_percent', 0):.2f}%")
            
            # 执行优化
            optimization_result = arranger.optimize_for_utilization()
            print(f"  优化执行结果: {'成功' if optimization_result else '失败/无改进'}")
            
            # 获取优化后的状态
            stats_after = arranger.get_layout_info()
            print(f"  优化后利用率: {stats_after.get('utilization_percent', 0):.2f}%")
            
            return True
        else:
            print(f"  ❌ 没有图片被成功放置，无法测试优化")
            return False
        
    except Exception as e:
        print(f"❌ 优化功能测试失败: {str(e)}")
        return False

def test_parameter_consistency():
    """测试参数一致性"""
    print("\n🧪 测试参数一致性")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器
        config_manager = ConfigManagerDuckDB()
        rectpack_settings = config_manager.get_rectpack_settings()
        
        # 创建arranger
        arranger = RectPackArranger(
            container_width=200,
            max_height=300,
            image_spacing=1
        )
        
        # 获取arranger的当前参数
        arranger_params = arranger.get_current_parameters()
        
        print(f"📋 参数一致性验证:")
        
        # 验证关键参数的一致性
        key_mappings = {
            'rectpack_rotation_enabled': 'rotation_enabled',
            'rectpack_sort_strategy': 'sort_key',
            'rectpack_pack_algorithm': 'pack_algo',
            'rectpack_enable_optimization': 'enable_optimization',
            'rectpack_optimization_iterations': 'optimization_iterations',
            'rectpack_min_utilization_threshold': 'min_utilization_threshold'
        }
        
        inconsistent_params = []
        for config_key, arranger_key in key_mappings.items():
            config_value = rectpack_settings.get(config_key)
            arranger_value = arranger_params.get(arranger_key)
            
            if config_value != arranger_value:
                inconsistent_params.append((config_key, config_value, arranger_value))
                print(f"  ❌ {config_key}: 配置={config_value}, Arranger={arranger_value}")
            else:
                print(f"  ✅ {config_key}: {config_value}")
        
        if inconsistent_params:
            print(f"  ❌ 发现 {len(inconsistent_params)} 个不一致的参数")
            return False
        else:
            print(f"  ✅ 所有关键参数都一致 ({len(key_mappings)} 个)")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数一致性测试失败: {str(e)}")
        return False

def generate_parameter_integration_test_report():
    """生成参数集成测试报告"""
    print("\n📊 RectPackArranger参数集成测试报告")
    print("=" * 80)
    
    # 执行所有测试
    test_results = {
        'parameter_loading': test_parameter_loading(),
        'parameter_reload': test_parameter_reload(),
        'current_parameters_method': test_current_parameters_method(),
        'optimization_with_parameters': test_optimization_with_parameters(),
        'parameter_consistency': test_parameter_consistency()
    }
    
    # 统计结果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    print(f"\n📋 测试结果汇总:")
    print(f"  参数加载功能: {'✅ 通过' if test_results['parameter_loading'] else '❌ 失败'}")
    print(f"  参数重新加载: {'✅ 通过' if test_results['parameter_reload'] else '❌ 失败'}")
    print(f"  获取当前参数: {'✅ 通过' if test_results['current_parameters_method'] else '❌ 失败'}")
    print(f"  优化功能集成: {'✅ 通过' if test_results['optimization_with_parameters'] else '❌ 失败'}")
    print(f"  参数一致性: {'✅ 通过' if test_results['parameter_consistency'] else '❌ 失败'}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print(f"🎉 RectPackArranger参数集成测试全部通过！")
        print(f"✅ RectPackArranger已成功集成新的参数系统")
        
        # 集成效果总结
        print(f"\n🎯 参数集成效果总结:")
        print(f"  1. ✅ 自动从配置加载19个RectPack参数")
        print(f"  2. ✅ 支持运行时参数重新加载")
        print(f"  3. ✅ 提供完整的参数获取接口")
        print(f"  4. ✅ 优化功能使用新的参数设置")
        print(f"  5. ✅ 配置管理器与Arranger参数保持一致")
        
        print(f"\n🚀 集成优势:")
        print(f"  • 配置驱动：所有参数都可通过配置管理器调整")
        print(f"  • 动态更新：支持运行时参数重新加载")
        print(f"  • 智能优化：优化算法使用配置的参数设置")
        print(f"  • 一致性保证：配置与实际使用参数完全一致")
        
    else:
        print(f"⚠️ RectPackArranger参数集成测试存在问题，需要进一步修复")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🔧 RectPackArranger参数集成测试")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证RectPackArranger正确加载配置参数")
    print("2. 🔍 验证参数重新加载功能")
    print("3. 🔍 验证参数获取接口完整性")
    print("4. 🔍 验证优化功能使用新参数")
    print("5. 🔍 验证配置与Arranger参数一致性")
    print("=" * 100)
    
    # 执行测试
    success = generate_parameter_integration_test_report()
    
    if success:
        print(f"\n🎯 参数集成验证结果:")
        print(f"✅ RectPackArranger参数集成完全成功")
        print(f"🚀 步骤3已完成：RectPackArranger现在使用新参数")
        print(f"📊 系统已完全迁移到RectPack参数体系")
    else:
        print(f"\n🔧 需要进一步修复:")
        print(f"1. 检查失败的测试用例")
        print(f"2. 完成剩余的参数集成工作")
        print(f"3. 重新验证集成效果")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段修复：图层管理优化

步骤2：修复Photoshop图层管理问题，防止图片覆盖

作者: PS画布修复团队
日期: 2024-12-19
版本: 第二阶段修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_layer_management_issues():
    """
    分析图层管理问题
    """
    print("🔍 第二阶段：分析图层管理问题")
    print("=" * 80)
    
    layer_issues = []
    
    print("📋 图层管理问题分析:")
    
    # 问题1: 图层堆叠顺序
    print("  1. 图层堆叠顺序问题:")
    print("     • 后放置的图层在上层，可能覆盖先放置的图层")
    print("     • 没有明确的图层顺序管理")
    print("     • 缺少图层命名和标识")
    
    layer_issues.append({
        'type': '图层堆叠顺序',
        'description': '后放置的图层覆盖先放置的图层',
        'severity': '高',
        'impact': '图片覆盖，布局错乱'
    })
    
    # 问题2: 图层属性设置
    print("  2. 图层属性设置问题:")
    print("     • 图层混合模式可能不正确")
    print("     • 图层透明度设置可能导致重叠效果")
    print("     • 图层锁定状态可能影响后续操作")
    
    layer_issues.append({
        'type': '图层属性设置',
        'description': '图层属性设置不当导致显示问题',
        'severity': '中',
        'impact': '图片显示异常，透明度问题'
    })
    
    # 问题3: 图层边界管理
    print("  3. 图层边界管理问题:")
    print("     • 图层bounds获取可能不准确")
    print("     • 图层尺寸与实际图片尺寸不匹配")
    print("     • 图层位置计算依赖不可靠的bounds")
    
    layer_issues.append({
        'type': '图层边界管理',
        'description': '图层边界信息不准确',
        'severity': '高',
        'impact': '坐标计算错误，位置偏移'
    })
    
    # 问题4: 图层创建和管理流程
    print("  4. 图层创建和管理流程问题:")
    print("     • 缺少图层创建前的验证")
    print("     • 没有图层创建后的验证")
    print("     • 缺少图层管理的统一接口")
    
    layer_issues.append({
        'type': '图层管理流程',
        'description': '图层创建和管理流程不完善',
        'severity': '中',
        'impact': '图层管理混乱，难以调试'
    })
    
    print(f"\n📊 发现 {len(layer_issues)} 个图层管理问题:")
    for i, issue in enumerate(layer_issues, 1):
        print(f"  {i}. {issue['type']} (严重程度: {issue['severity']})")
        print(f"     描述: {issue['description']}")
        print(f"     影响: {issue['impact']}")
    
    return layer_issues

def create_layer_management_optimization_plan():
    """
    创建图层管理优化方案
    """
    print("\n🔧 创建图层管理优化方案")
    print("=" * 80)
    
    optimization_plan = {
        'phase': '第二阶段',
        'target': '图层管理优化',
        'fixes': [
            {
                'id': 'FIX-005',
                'name': '图层顺序管理',
                'description': '确保图层按正确顺序创建，防止覆盖',
                'priority': '高',
                'implementation': 'implement_layer_order_management'
            },
            {
                'id': 'FIX-006',
                'name': '图层属性标准化',
                'description': '设置标准的图层属性（混合模式、透明度等）',
                'priority': '高',
                'implementation': 'standardize_layer_properties'
            },
            {
                'id': 'FIX-007',
                'name': '图层边界验证',
                'description': '验证图层边界信息的准确性',
                'priority': '中',
                'implementation': 'validate_layer_bounds'
            },
            {
                'id': 'FIX-008',
                'name': '图层命名和标识',
                'description': '为每个图层添加有意义的名称和标识',
                'priority': '中',
                'implementation': 'implement_layer_naming'
            },
            {
                'id': 'FIX-009',
                'name': '图层创建验证',
                'description': '添加图层创建前后的验证机制',
                'priority': '中',
                'implementation': 'add_layer_validation'
            }
        ]
    }
    
    print(f"📋 优化计划: {optimization_plan['phase']} - {optimization_plan['target']}")
    print(f"📋 优化项目: {len(optimization_plan['fixes'])} 个")
    
    for fix in optimization_plan['fixes']:
        print(f"\n  {fix['id']}: {fix['name']}")
        print(f"    描述: {fix['description']}")
        print(f"    优先级: {fix['priority']}")
        print(f"    实现方法: {fix['implementation']}")
    
    return optimization_plan

def implement_layer_order_management():
    """
    实现图层顺序管理
    """
    print("\n🔧 实现修复 FIX-005: 图层顺序管理")
    print("=" * 80)
    
    # 图层顺序管理策略
    layer_order_strategy = '''
# 图层顺序管理策略

## 问题分析
- Photoshop中后创建的图层默认在上层
- 这会导致后放置的图片覆盖先放置的图片
- RectPack算法计算的布局被图层顺序破坏

## 解决方案
1. 图层创建后立即移动到底层
2. 使用图层索引管理顺序
3. 确保图层按放置顺序排列

## JavaScript实现
```javascript
// 创建图层后立即移动到底层
function moveLayerToBottom(layer) {
    try {
        // 获取图层总数
        var totalLayers = app.activeDocument.layers.length;
        
        // 将当前图层移动到底层（最后一个位置）
        layer.move(app.activeDocument.layers[totalLayers - 1], ElementPlacement.PLACEAFTER);
        
        return "图层已移动到底层";
    } catch(e) {
        return "移动图层失败: " + e.toString();
    }
}

// 设置图层顺序
function setLayerOrder(layer, orderIndex) {
    try {
        // 根据索引设置图层位置
        if (orderIndex === 0) {
            // 移动到顶层
            layer.move(app.activeDocument.layers[0], ElementPlacement.PLACEBEFORE);
        } else {
            // 移动到指定位置
            layer.move(app.activeDocument.layers[orderIndex], ElementPlacement.PLACEAFTER);
        }
        
        return "图层顺序设置成功";
    } catch(e) {
        return "设置图层顺序失败: " + e.toString();
    }
}
```
'''
    
    print("📋 图层顺序管理策略:")
    print(layer_order_strategy)
    
    # 创建优化的JavaScript代码
    optimized_layer_js = '''
// 优化的图层顺序管理JavaScript代码
function manageLayerOrder(layerIndex, totalImages) {
    try {
        var doc = app.activeDocument;
        var currentLayer = doc.activeLayer;
        
        // 设置图层名称（包含顺序信息）
        currentLayer.name = "RectPack_Image_" + (layerIndex + 1) + "_of_" + totalImages;
        
        // 策略1: 将图层移动到底层，保持放置顺序
        var layers = doc.layers;
        if (layers.length > 1) {
            // 移动到底层
            currentLayer.move(layers[layers.length - 1], ElementPlacement.PLACEAFTER);
        }
        
        // 策略2: 设置图层属性确保正确显示
        currentLayer.blendMode = BlendMode.NORMAL;  // 正常混合模式
        currentLayer.opacity = 100;                 // 完全不透明
        currentLayer.visible = true;                // 确保可见
        
        return "图层顺序管理成功: " + currentLayer.name;
        
    } catch(e) {
        return "图层顺序管理失败: " + e.toString();
    }
}
'''
    
    print(f"\n📋 优化的图层管理JavaScript代码:")
    print(optimized_layer_js)
    
    return optimized_layer_js

def implement_layer_properties_standardization():
    """
    实现图层属性标准化
    """
    print("\n🔧 实现修复 FIX-006: 图层属性标准化")
    print("=" * 80)
    
    # 标准图层属性配置
    standard_properties = {
        'blendMode': 'NORMAL',      # 正常混合模式
        'opacity': 100,             # 完全不透明
        'visible': True,            # 可见
        'locked': False,            # 不锁定
        'fillOpacity': 100,         # 填充不透明度
        'kind': 'NORMAL'            # 普通图层
    }
    
    print("📋 标准图层属性配置:")
    for prop, value in standard_properties.items():
        print(f"  • {prop}: {value}")
    
    # JavaScript实现
    standardization_js = '''
// 图层属性标准化JavaScript代码
function standardizeLayerProperties(layer) {
    try {
        // 设置标准属性
        layer.blendMode = BlendMode.NORMAL;     // 正常混合模式
        layer.opacity = 100;                   // 完全不透明
        layer.fillOpacity = 100;               // 填充完全不透明
        layer.visible = true;                  // 确保可见
        
        // 确保图层不被锁定
        if (layer.allLocked) {
            layer.allLocked = false;
        }
        if (layer.positionLocked) {
            layer.positionLocked = false;
        }
        if (layer.transparentPixelsLocked) {
            layer.transparentPixelsLocked = false;
        }
        if (layer.imagePixelsLocked) {
            layer.imagePixelsLocked = false;
        }
        
        return "图层属性标准化成功";
        
    } catch(e) {
        return "图层属性标准化失败: " + e.toString();
    }
}
'''
    
    print(f"\n📋 图层属性标准化JavaScript代码:")
    print(standardization_js)
    
    return standardization_js

def implement_layer_bounds_validation():
    """
    实现图层边界验证
    """
    print("\n🔧 实现修复 FIX-007: 图层边界验证")
    print("=" * 80)
    
    # 边界验证策略
    validation_strategy = '''
# 图层边界验证策略

## 问题分析
- 图层bounds可能不准确
- 图层尺寸与预期不符
- 坐标计算依赖不可靠的bounds

## 验证要点
1. 验证图层实际尺寸
2. 验证图层位置
3. 验证图层边界完整性
4. 对比预期值与实际值

## 验证流程
1. 获取图层bounds
2. 计算实际尺寸
3. 对比预期尺寸
4. 验证位置准确性
5. 记录验证结果
'''
    
    print("📋 图层边界验证策略:")
    print(validation_strategy)
    
    # JavaScript验证代码
    validation_js = '''
// 图层边界验证JavaScript代码
function validateLayerBounds(expectedX, expectedY, expectedWidth, expectedHeight, tolerance) {
    try {
        var layer = app.activeDocument.activeLayer;
        var bounds = layer.bounds;
        
        // 获取实际边界
        var actualLeft = Math.round(bounds[0].value);
        var actualTop = Math.round(bounds[1].value);
        var actualRight = Math.round(bounds[2].value);
        var actualBottom = Math.round(bounds[3].value);
        
        // 计算实际尺寸
        var actualWidth = actualRight - actualLeft;
        var actualHeight = actualBottom - actualTop;
        
        // 验证位置
        var positionErrorX = Math.abs(actualLeft - expectedX);
        var positionErrorY = Math.abs(actualTop - expectedY);
        
        // 验证尺寸
        var sizeErrorWidth = Math.abs(actualWidth - expectedWidth);
        var sizeErrorHeight = Math.abs(actualHeight - expectedHeight);
        
        // 检查是否在容差范围内
        var positionValid = (positionErrorX <= tolerance) && (positionErrorY <= tolerance);
        var sizeValid = (sizeErrorWidth <= tolerance) && (sizeErrorHeight <= tolerance);
        
        var result = {
            valid: positionValid && sizeValid,
            actualPosition: [actualLeft, actualTop],
            actualSize: [actualWidth, actualHeight],
            expectedPosition: [expectedX, expectedY],
            expectedSize: [expectedWidth, expectedHeight],
            positionError: [positionErrorX, positionErrorY],
            sizeError: [sizeErrorWidth, sizeErrorHeight]
        };
        
        return JSON.stringify(result);
        
    } catch(e) {
        return "边界验证失败: " + e.toString();
    }
}
'''
    
    print(f"\n📋 图层边界验证JavaScript代码:")
    print(validation_js)
    
    return validation_js

def create_step2_implementation_plan():
    """
    创建步骤2的具体实施计划
    """
    print("\n📋 步骤2具体实施计划")
    print("=" * 80)
    
    implementation_plan = {
        'step': 2,
        'title': '图层管理优化',
        'duration': '2-3小时',
        'tasks': [
            {
                'task_id': 'T2-001',
                'name': '添加图层顺序管理',
                'description': '在PhotoshopHelper中添加图层顺序管理功能',
                'estimated_time': '45分钟',
                'files_to_modify': ['utils/photoshop_helper.py'],
                'priority': '高'
            },
            {
                'task_id': 'T2-002',
                'name': '实现图层属性标准化',
                'description': '确保所有图层使用标准属性',
                'estimated_time': '30分钟',
                'files_to_modify': ['utils/photoshop_helper.py'],
                'priority': '高'
            },
            {
                'task_id': 'T2-003',
                'name': '添加图层边界验证',
                'description': '验证图层边界信息的准确性',
                'estimated_time': '45分钟',
                'files_to_modify': ['utils/photoshop_helper.py'],
                'priority': '中'
            },
            {
                'task_id': 'T2-004',
                'name': '实现图层命名系统',
                'description': '为图层添加有意义的名称',
                'estimated_time': '30分钟',
                'files_to_modify': ['utils/photoshop_helper.py'],
                'priority': '中'
            },
            {
                'task_id': 'T2-005',
                'name': '创建图层管理测试',
                'description': '验证图层管理优化效果',
                'estimated_time': '30分钟',
                'files_to_modify': ['新建测试文件'],
                'priority': '中'
            }
        ]
    }
    
    print(f"📋 步骤: {implementation_plan['step']} - {implementation_plan['title']}")
    print(f"📋 预计耗时: {implementation_plan['duration']}")
    print(f"📋 任务数量: {len(implementation_plan['tasks'])} 个")
    
    for task in implementation_plan['tasks']:
        print(f"\n  {task['task_id']}: {task['name']}")
        print(f"    描述: {task['description']}")
        print(f"    预计时间: {task['estimated_time']}")
        print(f"    修改文件: {', '.join(task['files_to_modify'])}")
        print(f"    优先级: {task['priority']}")
    
    return implementation_plan

def main():
    """
    主函数：执行第二阶段修复
    """
    print("🚀 第二阶段修复：图层管理优化")
    print("=" * 100)
    print("目标:")
    print("1. 🔍 分析图层管理问题")
    print("2. 🔧 创建图层顺序管理方案")
    print("3. 🔧 实现图层属性标准化")
    print("4. 🔧 添加图层边界验证")
    print("5. 📋 制定具体实施计划")
    print("=" * 100)
    
    # 执行分析和修复
    results = {}
    
    # 步骤1: 分析图层管理问题
    layer_issues = analyze_layer_management_issues()
    results['issues_found'] = len(layer_issues)
    
    # 步骤2: 创建优化方案
    optimization_plan = create_layer_management_optimization_plan()
    results['optimization_plan'] = optimization_plan
    
    # 步骤3: 实现图层顺序管理
    layer_order_js = implement_layer_order_management()
    results['layer_order_implemented'] = True
    
    # 步骤4: 实现图层属性标准化
    properties_js = implement_layer_properties_standardization()
    results['properties_standardized'] = True
    
    # 步骤5: 实现图层边界验证
    validation_js = implement_layer_bounds_validation()
    results['bounds_validation_implemented'] = True
    
    # 步骤6: 创建实施计划
    implementation_plan = create_step2_implementation_plan()
    results['implementation_plan'] = implementation_plan
    
    # 输出总结
    print("\n" + "=" * 100)
    print("📊 第二阶段修复总结:")
    print("=" * 100)
    
    print(f"🔍 发现问题: {results['issues_found']} 个")
    print(f"🔧 优化方案: {len(optimization_plan['fixes'])} 个修复项")
    print(f"🔧 图层顺序: {'✅ 已实现' if results['layer_order_implemented'] else '❌ 未实现'}")
    print(f"🔧 属性标准化: {'✅ 已实现' if results['properties_standardized'] else '❌ 未实现'}")
    print(f"🔧 边界验证: {'✅ 已实现' if results['bounds_validation_implemented'] else '❌ 未实现'}")
    print(f"📋 实施计划: {len(implementation_plan['tasks'])} 个任务")
    
    print(f"\n🎯 下一步行动:")
    print(f"1. 🔥 立即执行: 添加图层顺序管理到PhotoshopHelper")
    print(f"2. 🔥 立即执行: 实现图层属性标准化")
    print(f"3. ⚡ 尽快执行: 添加图层边界验证")
    print(f"4. 💡 后续执行: 创建图层管理测试")
    
    print(f"\n✅ 第二阶段修复准备完成！")
    print(f"📁 准备好的图层管理优化代码可以立即应用到项目中")
    
    return len(layer_issues) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

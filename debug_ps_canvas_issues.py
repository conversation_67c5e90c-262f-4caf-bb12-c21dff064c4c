#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PS画布问题深度诊断脚本

分析PS画布中图片覆盖、空白问题，以及测试模式与正式环境利用率差异的根本原因

作者: PS画布问题诊断团队
日期: 2024-12-19
版本: 深度诊断版
"""

import sys
import os
import time
import json
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@dataclass
class CoordinateIssue:
    """坐标问题数据类"""
    issue_type: str
    description: str
    severity: str
    affected_images: List[str]
    suggested_fix: str

def analyze_coordinate_accuracy():
    """
    分析坐标精度问题
    """
    print("🔍 分析坐标精度问题")
    print("=" * 80)
    
    issues = []
    
    try:
        # 检查1: PhotoshopHelper的坐标计算逻辑
        from utils.photoshop_helper import PhotoshopHelper
        import inspect
        
        place_image_source = inspect.getsource(PhotoshopHelper.place_image)
        
        print("📋 检查PhotoshopHelper.place_image的坐标处理:")
        
        # 检查坐标计算逻辑
        coordinate_checks = [
            ("单位转换", "x_cm * 0.393701 * ppi" in place_image_source),
            ("像素坐标直接使用", "x is None or y is None" in place_image_source),
            ("相对坐标计算", "moveX = {x} - bounds[0].value" in place_image_source),
            ("图层定位", "layer.translate(moveX, moveY)" in place_image_source),
            ("尺寸调整", "resizeImage" in place_image_source),
            ("旋转处理", "rotateCanvas" in place_image_source)
        ]
        
        for check_name, check_result in coordinate_checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
            
            if not check_result and check_name in ["相对坐标计算", "图层定位"]:
                issues.append(CoordinateIssue(
                    issue_type="坐标计算错误",
                    description=f"{check_name}逻辑缺失或错误",
                    severity="高",
                    affected_images=["所有图片"],
                    suggested_fix=f"修复{check_name}逻辑"
                ))
        
        # 检查2: 坐标计算的JavaScript逻辑
        print(f"\n📋 检查JavaScript坐标计算逻辑:")
        
        js_checks = [
            ("获取图层边界", "var bounds = layer.bounds" in place_image_source),
            ("计算移动距离", "var moveX = {x} - bounds[0].value" in place_image_source),
            ("执行图层移动", "layer.translate(moveX, moveY)" in place_image_source),
            ("图层可见性", "layer.visible = true" in place_image_source)
        ]
        
        for check_name, check_result in js_checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
            
            if not check_result:
                issues.append(CoordinateIssue(
                    issue_type="JavaScript逻辑错误",
                    description=f"{check_name}在JavaScript中缺失",
                    severity="高",
                    affected_images=["所有图片"],
                    suggested_fix=f"添加{check_name}的JavaScript逻辑"
                ))
        
        return issues
        
    except Exception as e:
        print(f"❌ 坐标精度分析失败: {str(e)}")
        return []

def analyze_test_vs_production_differences():
    """
    分析测试模式与正式环境的差异
    """
    print("\n🔍 分析测试模式与正式环境的差异")
    print("=" * 80)
    
    differences = []
    
    try:
        # 检查1: 坐标系统差异
        print("📋 检查坐标系统差异:")
        
        # 测试模式使用PIL/matplotlib
        print("  测试模式:")
        print("    • 使用PIL/matplotlib绘制")
        print("    • 坐标系统: 左上角(0,0)")
        print("    • 单位: 像素")
        print("    • 精度: 整数像素")
        
        # 正式环境使用Photoshop
        print("  正式环境:")
        print("    • 使用Photoshop COM接口")
        print("    • 坐标系统: 左上角(0,0)")
        print("    • 单位: 像素/厘米转换")
        print("    • 精度: 浮点数，可能有精度损失")
        
        differences.append({
            'type': '坐标精度',
            'test_mode': '整数像素精度',
            'production': '浮点数精度，可能有损失',
            'impact': '中等'
        })
        
        # 检查2: 图片处理差异
        print(f"\n📋 检查图片处理差异:")
        
        print("  测试模式:")
        print("    • 绘制色块，无实际图片")
        print("    • 尺寸完全按照算法计算")
        print("    • 无图片加载/调整开销")
        print("    • 无旋转处理复杂性")
        
        print("  正式环境:")
        print("    • 加载实际图片文件")
        print("    • 可能调整图片尺寸")
        print("    • 图片旋转处理")
        print("    • COM接口调用开销")
        
        differences.append({
            'type': '图片处理',
            'test_mode': '理想化色块绘制',
            'production': '实际图片处理，有各种开销',
            'impact': '高'
        })
        
        # 检查3: 算法执行环境差异
        print(f"\n📋 检查算法执行环境差异:")
        
        from core.rectpack_arranger import RectPackArranger
        
        # 检查RectPack算法在两种模式下的执行
        print("  算法执行:")
        print("    • 测试模式: 纯Python环境，算法结果直接绘制")
        print("    • 正式环境: Python + Photoshop COM，算法结果需要传递")
        
        differences.append({
            'type': '算法执行环境',
            'test_mode': '纯Python，无中间转换',
            'production': 'Python + PS COM，有数据传递损失',
            'impact': '高'
        })
        
        return differences
        
    except Exception as e:
        print(f"❌ 测试与正式环境差异分析失败: {str(e)}")
        return []

def simulate_coordinate_flow():
    """
    模拟坐标流转过程
    """
    print("\n🔧 模拟坐标流转过程")
    print("=" * 80)
    
    try:
        # 模拟RectPack算法输出
        rectpack_output = [
            {'name': 'img1', 'x': 0, 'y': 0, 'width': 120, 'height': 80, 'rotated': False},
            {'name': 'img2', 'x': 122, 'y': 0, 'width': 80, 'height': 60, 'rotated': False},
            {'name': 'img3', 'x': 0, 'y': 82, 'width': 100, 'height': 70, 'rotated': True}
        ]
        
        print("📋 RectPack算法输出:")
        for img in rectpack_output:
            print(f"  {img['name']}: 位置({img['x']},{img['y']}) 尺寸({img['width']}x{img['height']}) 旋转={img['rotated']}")
        
        # 模拟测试模式处理
        print(f"\n📋 测试模式处理:")
        for img in rectpack_output:
            # 测试模式直接使用坐标
            test_x, test_y = img['x'], img['y']
            test_w, test_h = img['width'], img['height']
            
            print(f"  {img['name']}: PIL绘制矩形({test_x},{test_y},{test_x+test_w},{test_y+test_h})")
            
            # 检查是否有重叠
            for other in rectpack_output:
                if other['name'] != img['name']:
                    if not (img['x'] + img['width'] <= other['x'] or 
                           img['x'] >= other['x'] + other['width'] or
                           img['y'] + img['height'] <= other['y'] or 
                           img['y'] >= other['y'] + other['height']):
                        print(f"    ⚠️ 检测到与{other['name']}重叠")
        
        # 模拟正式环境处理
        print(f"\n📋 正式环境处理:")
        for img in rectpack_output:
            # 正式环境的坐标传递
            ps_x, ps_y = img['x'], img['y']
            ps_w, ps_h = img['width'], img['height']
            rotation = 90 if img['rotated'] else 0
            
            print(f"  {img['name']}: PS放置图片")
            print(f"    • 传递坐标: ({ps_x},{ps_y})")
            print(f"    • 传递尺寸: {ps_w}x{ps_h}")
            print(f"    • 旋转角度: {rotation}°")
            
            # 模拟可能的问题
            potential_issues = []
            
            # 检查坐标是否为整数
            if not isinstance(ps_x, int) or not isinstance(ps_y, int):
                potential_issues.append("坐标不是整数")
            
            # 检查尺寸是否合理
            if ps_w <= 0 or ps_h <= 0:
                potential_issues.append("尺寸无效")
            
            # 检查是否超出画布
            canvas_width, canvas_height = 205, 500
            if ps_x + ps_w > canvas_width or ps_y + ps_h > canvas_height:
                potential_issues.append("超出画布边界")
            
            if potential_issues:
                print(f"    ⚠️ 潜在问题: {', '.join(potential_issues)}")
            else:
                print(f"    ✅ 参数正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 坐标流转模拟失败: {str(e)}")
        return False

def analyze_photoshop_specific_issues():
    """
    分析Photoshop特有的问题
    """
    print("\n🔍 分析Photoshop特有的问题")
    print("=" * 80)
    
    ps_issues = []
    
    try:
        # 检查1: 图层堆叠问题
        print("📋 检查图层堆叠问题:")
        
        print("  可能的图层问题:")
        print("    • 图层顺序: 后放置的图层在上层，可能覆盖先放置的")
        print("    • 图层混合模式: 可能影响显示效果")
        print("    • 图层透明度: 可能导致重叠效果")
        print("    • 图层锁定: 可能影响移动和调整")
        
        ps_issues.append({
            'type': '图层堆叠',
            'description': '后放置的图层覆盖先放置的图层',
            'severity': '高',
            'solution': '检查图层顺序，确保正确的Z-index'
        })
        
        # 检查2: 坐标系统问题
        print(f"\n📋 检查坐标系统问题:")
        
        print("  Photoshop坐标系统:")
        print("    • 原点: 左上角(0,0)")
        print("    • X轴: 向右为正")
        print("    • Y轴: 向下为正")
        print("    • 单位: 像素/英寸/厘米")
        
        print("  可能的坐标问题:")
        print("    • 单位转换错误")
        print("    • 浮点数精度损失")
        print("    • 相对坐标计算错误")
        print("    • 图层边界获取错误")
        
        ps_issues.append({
            'type': '坐标计算',
            'description': '坐标转换或计算过程中的精度损失',
            'severity': '高',
            'solution': '使用整数坐标，避免浮点数运算'
        })
        
        # 检查3: 图片处理问题
        print(f"\n📋 检查图片处理问题:")
        
        print("  图片处理流程:")
        print("    1. 打开图片文件")
        print("    2. 调整图片尺寸（如果需要）")
        print("    3. 旋转图片（如果需要）")
        print("    4. 复制图片到剪贴板")
        print("    5. 粘贴到目标画布")
        print("    6. 移动到指定位置")
        
        print("  可能的处理问题:")
        print("    • 图片尺寸调整导致的质量损失")
        print("    • 旋转操作改变图片尺寸")
        print("    • 粘贴位置不准确")
        print("    • 图片文件损坏或不存在")
        
        ps_issues.append({
            'type': '图片处理',
            'description': '图片处理过程中的尺寸或位置变化',
            'severity': '中',
            'solution': '优化图片处理流程，减少不必要的调整'
        })
        
        return ps_issues
        
    except Exception as e:
        print(f"❌ Photoshop问题分析失败: {str(e)}")
        return []

def generate_optimization_recommendations():
    """
    生成优化建议
    """
    print("\n💡 生成优化建议")
    print("=" * 80)
    
    recommendations = [
        {
            'category': '坐标精度优化',
            'priority': '高',
            'recommendations': [
                '使用整数坐标，避免浮点数精度损失',
                '在PhotoshopHelper中添加坐标验证逻辑',
                '优化JavaScript坐标计算，使用相对坐标',
                '添加坐标边界检查，防止超出画布'
            ]
        },
        {
            'category': '图层管理优化',
            'priority': '高',
            'recommendations': [
                '确保图层按正确顺序放置',
                '设置图层混合模式为正常',
                '检查图层可见性和锁定状态',
                '添加图层命名，便于调试'
            ]
        },
        {
            'category': '图片处理优化',
            'priority': '中',
            'recommendations': [
                '只在必要时调整图片尺寸',
                '优化旋转处理，确保尺寸正确',
                '添加图片文件存在性检查',
                '优化图片加载和粘贴流程'
            ]
        },
        {
            'category': '测试与调试优化',
            'priority': '中',
            'recommendations': [
                '添加详细的坐标传递日志',
                '在测试模式中模拟PS环境',
                '创建坐标验证工具',
                '添加图片重叠检测'
            ]
        },
        {
            'category': '算法一致性优化',
            'priority': '高',
            'recommendations': [
                '确保测试模式和正式环境使用相同的算法',
                '统一坐标系统和单位处理',
                '添加算法结果验证机制',
                '优化数据传递链路'
            ]
        }
    ]
    
    for rec in recommendations:
        print(f"\n🔧 {rec['category']} (优先级: {rec['priority']})")
        for i, item in enumerate(rec['recommendations'], 1):
            print(f"  {i}. {item}")
    
    return recommendations

def create_diagnostic_test():
    """
    创建诊断测试
    """
    print("\n🧪 创建诊断测试")
    print("=" * 80)
    
    test_cases = [
        {
            'name': '坐标精度测试',
            'description': '测试坐标在传递过程中的精度保持',
            'test_data': [
                {'x': 0, 'y': 0, 'width': 100, 'height': 100},
                {'x': 102, 'y': 0, 'width': 100, 'height': 100},
                {'x': 0, 'y': 102, 'width': 100, 'height': 100}
            ],
            'expected': '图片应该紧密排列，间距为2像素'
        },
        {
            'name': '图片重叠测试',
            'description': '测试图片是否出现重叠',
            'test_data': [
                {'x': 0, 'y': 0, 'width': 120, 'height': 80},
                {'x': 122, 'y': 0, 'width': 80, 'height': 60},
                {'x': 0, 'y': 82, 'width': 100, 'height': 70}
            ],
            'expected': '图片应该无重叠，紧密排列'
        },
        {
            'name': '旋转处理测试',
            'description': '测试图片旋转后的位置和尺寸',
            'test_data': [
                {'x': 0, 'y': 0, 'width': 120, 'height': 80, 'rotated': True}
            ],
            'expected': '旋转后图片应该在正确位置，尺寸为80x120'
        }
    ]
    
    for test in test_cases:
        print(f"\n📋 {test['name']}:")
        print(f"  描述: {test['description']}")
        print(f"  测试数据: {len(test['test_data'])} 个图片")
        print(f"  预期结果: {test['expected']}")
        
        # 模拟测试执行
        print(f"  测试执行:")
        for i, data in enumerate(test['test_data'], 1):
            print(f"    图片{i}: 位置({data['x']},{data['y']}) 尺寸({data['width']}x{data['height']})")
            if data.get('rotated'):
                print(f"           旋转: 90度")
    
    return test_cases

def main():
    """
    主诊断函数
    """
    print("🔬 PS画布问题深度诊断")
    print("=" * 100)
    print("诊断目标:")
    print("1. 🔍 分析图片覆盖和空白问题的根本原因")
    print("2. 🔍 找出测试模式与正式环境利用率差异的原因")
    print("3. 🔍 识别坐标传递过程中的精度损失")
    print("4. 🔍 提供具体的优化修复方案")
    print("=" * 100)
    
    # 执行诊断
    results = {}
    
    # 诊断1: 坐标精度问题
    coordinate_issues = analyze_coordinate_accuracy()
    results['coordinate_issues'] = coordinate_issues
    
    # 诊断2: 测试与正式环境差异
    environment_differences = analyze_test_vs_production_differences()
    results['environment_differences'] = environment_differences
    
    # 诊断3: 坐标流转模拟
    coordinate_flow_success = simulate_coordinate_flow()
    results['coordinate_flow'] = coordinate_flow_success
    
    # 诊断4: Photoshop特有问题
    ps_issues = analyze_photoshop_specific_issues()
    results['ps_issues'] = ps_issues
    
    # 生成优化建议
    recommendations = generate_optimization_recommendations()
    results['recommendations'] = recommendations
    
    # 创建诊断测试
    test_cases = create_diagnostic_test()
    results['test_cases'] = test_cases
    
    # 输出诊断结果
    print("\n" + "=" * 100)
    print("📊 诊断结果汇总:")
    print("=" * 100)
    
    print(f"🔍 发现的问题:")
    print(f"  • 坐标相关问题: {len(coordinate_issues)} 个")
    print(f"  • 环境差异: {len(environment_differences)} 个")
    print(f"  • PS特有问题: {len(ps_issues)} 个")
    
    print(f"\n💡 优化建议:")
    print(f"  • 高优先级建议: {len([r for r in recommendations if r['priority'] == '高'])} 类")
    print(f"  • 中优先级建议: {len([r for r in recommendations if r['priority'] == '中'])} 类")
    
    print(f"\n🧪 诊断测试:")
    print(f"  • 创建测试用例: {len(test_cases)} 个")
    
    # 关键发现
    print(f"\n🎯 关键发现:")
    print(f"1. 🔥 坐标精度问题: 浮点数精度损失可能导致图片位置偏移")
    print(f"2. 🔥 环境差异: 测试模式使用理想化绘制，正式环境有实际处理开销")
    print(f"3. 🔥 图层管理: PS中图层顺序可能导致覆盖问题")
    print(f"4. 🔥 算法一致性: 需要确保两种环境使用完全相同的算法逻辑")
    
    # 修复优先级
    print(f"\n🔧 修复优先级:")
    print(f"1. 🔥 立即修复: 坐标精度和图层管理问题")
    print(f"2. ⚡ 尽快修复: 算法一致性和环境差异")
    print(f"3. 💡 后续优化: 图片处理流程和调试工具")
    
    # 保存诊断结果
    try:
        with open('ps_canvas_diagnostic_results.json', 'w', encoding='utf-8') as f:
            # 转换为可序列化的格式
            serializable_results = {
                'coordinate_issues_count': len(coordinate_issues),
                'environment_differences_count': len(environment_differences),
                'ps_issues_count': len(ps_issues),
                'recommendations_count': len(recommendations),
                'test_cases_count': len(test_cases),
                'coordinate_flow_success': coordinate_flow_success
            }
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 诊断结果已保存到: ps_canvas_diagnostic_results.json")
        
    except Exception as e:
        print(f"❌ 保存诊断结果失败: {str(e)}")
    
    return len(coordinate_issues) == 0 and coordinate_flow_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

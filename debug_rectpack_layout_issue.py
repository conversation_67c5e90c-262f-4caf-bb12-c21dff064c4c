#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack算法布局问题深度诊断脚本

诊断三个核心问题：
1. 没有TIFF的说明文档
2. 没有在PS中关闭已保存的画布，以节省内存
3. 在PS画布排列布局图片，并没有rectpack算法的布局逻辑和效果，只是图片依次排列在一起

作者: RectPack算法诊断团队
日期: 2024-12-19
版本: 深度诊断版
"""

import sys
import os
import time
import inspect
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def diagnose_rectpack_layout_flow():
    """
    诊断RectPack算法的完整布局流程
    """
    print("🔍 深度诊断RectPack算法布局流程")
    print("=" * 80)

    try:
        # 模拟测试数据 - 添加必要的字段
        test_patterns = [
            {
                'pattern_name': 'test1',
                'width_cm': 120,
                'height_cm': 80,
                'quantity': 1,
                'path': '/fake/path/test1.jpg',  # 模拟路径
                'index': 0,
                'row_number': 1
            },
            {
                'pattern_name': 'test2',
                'width_cm': 80,
                'height_cm': 60,
                'quantity': 1,
                'path': '/fake/path/test2.jpg',  # 模拟路径
                'index': 1,
                'row_number': 2
            },
            {
                'pattern_name': 'test3',
                'width_cm': 100,
                'height_cm': 70,
                'quantity': 1,
                'path': '/fake/path/test3.jpg',  # 模拟路径
                'index': 2,
                'row_number': 3
            },
        ]

        print(f"📋 测试数据: {len(test_patterns)} 张图片")
        for i, pattern in enumerate(test_patterns, 1):
            print(f"  {i}. {pattern['pattern_name']}: {pattern['width_cm']}x{pattern['height_cm']}cm")

        # 初始化UnifiedImageArranger
        from core.unified_image_arranger import UnifiedImageArranger

        arranger = UnifiedImageArranger()

        # 设置画布参数
        canvas_width_px = 205 * 72 // 2.54  # 205cm转换为像素
        max_height_px = 500 * 72 // 2.54    # 500cm转换为像素
        image_spacing_px = 1 * 72 // 2.54   # 1cm转换为像素
        ppi = 72

        print(f"\n🎨 画布参数:")
        print(f"  宽度: {canvas_width_px}px")
        print(f"  最大高度: {max_height_px}px")
        print(f"  图片间距: {image_spacing_px}px")
        print(f"  PPI: {ppi}")

        # 初始化排列器
        arranger.initialize(
            canvas_width_px=canvas_width_px,
            max_height_px=max_height_px,
            image_spacing_px=image_spacing_px,
            ppi=ppi
        )

        print(f"\n🔧 开始RectPack算法排列...")

        # 执行排列
        arranged_images = arranger.arrange_images(test_patterns)

        print(f"\n📊 排列结果分析:")
        print(f"  输入图片数: {len(test_patterns)}")
        print(f"  成功排列数: {len(arranged_images)}")

        if arranged_images:
            print(f"\n📍 详细布局信息:")
            for i, img in enumerate(arranged_images, 1):
                x = img.get('x', 0)
                y = img.get('y', 0)
                width = img.get('width', 0)
                height = img.get('height', 0)
                rotated = img.get('need_rotation', False)
                name = img.get('name', f'Image_{i}')

                print(f"  {i}. {name}:")
                print(f"     位置: ({x}, {y})")
                print(f"     尺寸: {width}x{height}px")
                print(f"     旋转: {'是' if rotated else '否'}")

                # 检查坐标是否合理
                if x == 0 and y == 0 and i > 1:
                    print(f"     ⚠️ 警告: 坐标可能不正确，多个图片都在(0,0)位置")

                if width <= 0 or height <= 0:
                    print(f"     ❌ 错误: 尺寸无效")

        # 获取布局统计
        stats = arranger.get_layout_statistics()
        if stats:
            print(f"\n📈 布局统计:")
            print(f"  画布利用率: {stats.get('utilization_percent', 0):.2f}%")
            print(f"  画布尺寸: {stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")
            print(f"  已用面积: {stats.get('used_area', 0)}px²")
            print(f"  总面积: {stats.get('total_area', 0)}px²")

        return arranged_images

    except Exception as e:
        print(f"❌ 诊断失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def diagnose_rectpack_algorithm_core():
    """
    诊断RectPack算法核心逻辑
    """
    print("\n🔧 诊断RectPack算法核心逻辑")
    print("=" * 80)

    try:
        from core.rectpack_arranger import RectPackArranger

        # 初始化参数
        canvas_width_px = 205 * 72 // 2.54  # 205cm
        max_height_px = 500 * 72 // 2.54    # 500cm
        image_spacing_px = 1 * 72 // 2.54   # 1cm

        # 创建RectPack排列器 - 使用正确的初始化参数
        arranger = RectPackArranger(
            container_width=canvas_width_px,
            image_spacing=image_spacing_px,
            max_height=max_height_px
        )

        print(f"📋 RectPack排列器初始化完成")
        print(f"  画布宽度: {canvas_width_px}px")
        print(f"  最大高度: {max_height_px}px")
        print(f"  图片间距: {image_spacing_px}px")

        # 测试图片放置
        test_images = [
            {'width': 120, 'height': 80, 'name': 'test1'},
            {'width': 80, 'height': 60, 'name': 'test2'},
            {'width': 100, 'height': 70, 'name': 'test3'},
        ]

        print(f"\n🎯 测试图片放置:")
        placed_images = []

        for i, img in enumerate(test_images, 1):
            width_px = img['width'] * 72 // 2.54  # cm转px
            height_px = img['height'] * 72 // 2.54

            print(f"\n  测试图片 {i}: {img['name']} ({width_px}x{height_px}px)")

            # 调用RectPack算法
            x, y, success = arranger.place_image(width_px, height_px, img)

            if success:
                print(f"    ✅ 成功放置在 ({x}, {y})")
                placed_images.append({
                    'name': img['name'],
                    'x': x,
                    'y': y,
                    'width': width_px,
                    'height': height_px
                })
            else:
                print(f"    ❌ 放置失败")

        # 检查放置结果
        print(f"\n📊 放置结果分析:")
        print(f"  成功放置: {len(placed_images)}/{len(test_images)} 张图片")

        if len(placed_images) > 1:
            # 检查是否有重叠
            for i, img1 in enumerate(placed_images):
                for j, img2 in enumerate(placed_images[i+1:], i+1):
                    if (img1['x'] < img2['x'] + img2['width'] and
                        img1['x'] + img1['width'] > img2['x'] and
                        img1['y'] < img2['y'] + img2['height'] and
                        img1['y'] + img1['height'] > img2['y']):
                        print(f"    ⚠️ 警告: {img1['name']} 和 {img2['name']} 可能重叠")
                    else:
                        print(f"    ✅ {img1['name']} 和 {img2['name']} 无重叠")

        # 检查是否是简单的依次排列
        if len(placed_images) >= 2:
            # 检查是否所有图片都在同一行（y坐标相同）
            y_coords = [img['y'] for img in placed_images]
            if len(set(y_coords)) == 1:
                print(f"    ⚠️ 警告: 所有图片都在同一行，可能是简单的依次排列")
            else:
                print(f"    ✅ 图片分布在不同行，符合RectPack算法特征")

            # 检查是否有紧密排列
            for i in range(len(placed_images) - 1):
                img1 = placed_images[i]
                img2 = placed_images[i + 1]

                # 计算水平间距
                if img1['y'] == img2['y']:  # 同一行
                    gap = img2['x'] - (img1['x'] + img1['width'])
                    if gap > image_spacing_px * 2:  # 间距过大
                        print(f"    ⚠️ 警告: {img1['name']} 和 {img2['name']} 间距过大 ({gap}px)")
                    else:
                        print(f"    ✅ {img1['name']} 和 {img2['name']} 间距合理 ({gap}px)")

        return placed_images

    except Exception as e:
        print(f"❌ RectPack算法诊断失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def diagnose_ps_integration_issues():
    """
    诊断PS集成问题
    """
    print("\n🔧 诊断PS集成问题")
    print("=" * 80)

    try:
        from ui.rectpack_layout_worker import RectPackLayoutWorker

        # 检查_create_photoshop_canvas方法
        method_source = inspect.getsource(RectPackLayoutWorker._create_photoshop_canvas)

        print("📋 检查PS集成关键步骤:")

        # 检查问题1: 说明文档生成
        if "generate_description" in method_source:
            print("  ✅ 问题1: 包含说明文档生成步骤")
        else:
            print("  ❌ 问题1: 缺少说明文档生成步骤")

        # 检查问题2: 画布关闭
        if "close_canvas" in method_source:
            print("  ✅ 问题2: 包含画布关闭步骤")
        else:
            print("  ❌ 问题2: 缺少画布关闭步骤")

        if "cleanup" in method_source:
            print("  ✅ 问题2: 包含资源清理步骤")
        else:
            print("  ❌ 问题2: 缺少资源清理步骤")

        # 检查问题3: RectPack布局逻辑
        print("\n📋 检查RectPack布局逻辑:")

        if "arranged_images" in method_source:
            print("  ✅ 使用arranged_images参数")
        else:
            print("  ❌ 未使用arranged_images参数")

        if "place_info" in method_source:
            print("  ✅ 构建place_info数据结构")
        else:
            print("  ❌ 未构建place_info数据结构")

        # 检查坐标传递
        if "image_info.get('x'" in method_source:
            print("  ✅ 从arranged_images提取x坐标")
        else:
            print("  ❌ 未从arranged_images提取x坐标")

        if "image_info.get('y'" in method_source:
            print("  ✅ 从arranged_images提取y坐标")
        else:
            print("  ❌ 未从arranged_images提取y坐标")

        # 检查是否有简化逻辑
        if "依次排列" in method_source or "sequential" in method_source.lower():
            print("  ⚠️ 警告: 可能包含简化的依次排列逻辑")
        else:
            print("  ✅ 未发现简化的依次排列逻辑")

        return True

    except Exception as e:
        print(f"❌ PS集成诊断失败: {str(e)}")
        return False

def diagnose_image_processor_place_image():
    """
    诊断image_processor.place_image方法
    """
    print("\n🔧 诊断image_processor.place_image方法")
    print("=" * 80)

    try:
        from utils.image_processor import PhotoshopImageProcessor

        processor = PhotoshopImageProcessor()

        # 检查place_image方法
        method_source = inspect.getsource(processor.place_image)

        print("📋 检查place_image方法实现:")

        # 检查是否正确提取坐标
        if "image_info.get('x'" in method_source:
            print("  ✅ 正确提取x坐标")
        else:
            print("  ❌ 未正确提取x坐标")

        if "image_info.get('y'" in method_source:
            print("  ✅ 正确提取y坐标")
        else:
            print("  ❌ 未正确提取y坐标")

        # 检查是否正确提取尺寸
        if "image_info.get('width'" in method_source:
            print("  ✅ 正确提取width")
        else:
            print("  ❌ 未正确提取width")

        if "image_info.get('height'" in method_source:
            print("  ✅ 正确提取height")
        else:
            print("  ❌ 未正确提取height")

        # 检查是否有坐标转换
        if "转换" in method_source or "convert" in method_source.lower():
            print("  ⚠️ 警告: 可能包含坐标转换逻辑")
        else:
            print("  ✅ 未发现坐标转换逻辑")

        # 检查是否直接传递给PhotoshopHelper
        if "self.ps_helper.place_image" in method_source:
            print("  ✅ 直接调用PhotoshopHelper.place_image")
        else:
            print("  ❌ 未直接调用PhotoshopHelper.place_image")

        return True

    except Exception as e:
        print(f"❌ image_processor诊断失败: {str(e)}")
        return False

def simulate_complete_rectpack_flow():
    """
    模拟完整的RectPack流程
    """
    print("\n🔧 模拟完整的RectPack流程")
    print("=" * 80)

    try:
        # 第一步: 排列图片
        print("📋 第一步: 使用RectPack算法排列图片")
        arranged_images = diagnose_rectpack_layout_flow()

        if not arranged_images:
            print("❌ 图片排列失败，无法继续")
            return False

        # 第二步: 模拟PS放置
        print("\n📋 第二步: 模拟PS图片放置")

        for i, img in enumerate(arranged_images, 1):
            x = img.get('x', 0)
            y = img.get('y', 0)
            width = img.get('width', 0)
            height = img.get('height', 0)
            name = img.get('name', f'Image_{i}')
            rotated = img.get('need_rotation', False)

            print(f"  {i}. 放置 {name}:")
            print(f"     PS坐标: ({x}, {y})")
            print(f"     PS尺寸: {width}x{height}px")
            print(f"     PS旋转: {90 if rotated else 0}度")

            # 检查坐标合理性
            if x < 0 or y < 0:
                print(f"     ❌ 错误: 坐标为负数")
            elif width <= 0 or height <= 0:
                print(f"     ❌ 错误: 尺寸无效")
            else:
                print(f"     ✅ 参数正常")

        # 第三步: 检查布局效果
        print("\n📋 第三步: 检查布局效果")

        # 检查是否是RectPack布局还是简单排列
        if len(arranged_images) >= 2:
            # 计算图片分布
            x_coords = [img.get('x', 0) for img in arranged_images]
            y_coords = [img.get('y', 0) for img in arranged_images]

            unique_x = len(set(x_coords))
            unique_y = len(set(y_coords))

            print(f"  X坐标分布: {unique_x} 个不同值")
            print(f"  Y坐标分布: {unique_y} 个不同值")

            if unique_x == len(arranged_images) and unique_y == 1:
                print("  ⚠️ 警告: 所有图片在同一行，可能是简单的水平排列")
            elif unique_y == len(arranged_images) and unique_x == 1:
                print("  ⚠️ 警告: 所有图片在同一列，可能是简单的垂直排列")
            else:
                print("  ✅ 图片分布合理，符合RectPack算法特征")

        return True

    except Exception as e:
        print(f"❌ 完整流程模拟失败: {str(e)}")
        return False

def main():
    """
    主诊断函数
    """
    print("🔬 RectPack算法布局问题深度诊断")
    print("=" * 100)
    print("诊断目标:")
    print("1. 🔍 没有TIFF的说明文档")
    print("2. 🔍 没有在PS中关闭已保存的画布，以节省内存")
    print("3. 🔍 在PS画布排列布局图片，并没有rectpack算法的布局逻辑和效果，只是图片依次排列在一起")
    print("=" * 100)

    # 执行诊断
    results = []

    # 诊断1: RectPack算法核心逻辑
    print("\n" + "="*50 + " 诊断1: RectPack算法核心逻辑 " + "="*50)
    result1 = diagnose_rectpack_algorithm_core()
    results.append(("RectPack算法核心逻辑", len(result1) > 0))

    # 诊断2: PS集成问题
    print("\n" + "="*50 + " 诊断2: PS集成问题 " + "="*50)
    result2 = diagnose_ps_integration_issues()
    results.append(("PS集成问题", result2))

    # 诊断3: image_processor.place_image方法
    print("\n" + "="*50 + " 诊断3: image_processor.place_image方法 " + "="*50)
    result3 = diagnose_image_processor_place_image()
    results.append(("image_processor.place_image方法", result3))

    # 诊断4: 完整流程模拟
    print("\n" + "="*50 + " 诊断4: 完整流程模拟 " + "="*50)
    result4 = simulate_complete_rectpack_flow()
    results.append(("完整流程模拟", result4))

    # 输出诊断结果
    print("\n" + "=" * 100)
    print("📊 深度诊断结果汇总:")
    print("=" * 100)

    passed_count = 0
    for test_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1

    print(f"\n📈 总体结果: {passed_count}/{len(results)} 项诊断正常")

    if passed_count == len(results):
        print("🎉 所有诊断正常！RectPack算法工作正常！")
    else:
        print("⚠️ 发现问题，需要进一步修复。")

    print("\n💡 问题分析:")
    print("如果发现问题3（图片依次排列），可能的原因：")
    print("1. RectPack算法本身工作正常，但arranged_images中的坐标不正确")
    print("2. image_processor.place_image方法没有正确传递坐标")
    print("3. PhotoshopHelper.place_image方法没有正确处理坐标")
    print("4. 存在坐标转换或覆盖逻辑")

    print("\n🔧 建议修复步骤:")
    print("1. 检查arranged_images中的坐标是否正确")
    print("2. 确认image_processor.place_image正确传递坐标")
    print("3. 验证PhotoshopHelper.place_image正确处理坐标")
    print("4. 移除任何简化的排列逻辑")

    return passed_count == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

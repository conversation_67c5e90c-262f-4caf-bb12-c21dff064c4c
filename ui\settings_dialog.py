#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置对话框模块

提供应用设置界面：
1. 画布设置
2. Photoshop设置
3. 其他偏好设置
"""

import os
import logging
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QSpinBox, QDoubleSpinBox, QCheckBox,
    QComboBox, QDialogButtonBox, QTabWidget,
    QWidget, QFormLayout, QGroupBox, QFrame
)
from PyQt6.QtCore import Qt

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("SettingsDialog")

class SettingsDialog(QDialog):
    """设置对话框类"""

    def __init__(self, config_manager, parent=None):
        """初始化设置对话框

        Args:
            config_manager: 配置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        self.config_manager = config_manager
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("设置")
        self.setMinimumWidth(400)

        # 创建主布局
        layout = QVBoxLayout(self)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 添加画布设置选项卡
        canvas_tab = QWidget()
        canvas_layout = QFormLayout(canvas_tab)

        self._init_canvas_ui(canvas_layout)

        tab_widget.addTab(canvas_tab, "画布设置")

        # 添加排列设置选项卡
        layout_tab = QWidget()
        layout_layout = QFormLayout(layout_tab)

        # RectPack算法基础设置组
        rectpack_basic_group = QGroupBox("RectPack算法基础设置")
        rectpack_basic_layout = QFormLayout(rectpack_basic_group)

        # 启用RectPack算法
        self.use_rectpack_algorithm = QCheckBox("启用RectPack算法")
        self.use_rectpack_algorithm.setToolTip("启用后，使用RectPack算法替代传统的A/B/C分类算法，获得更高的画布利用率")
        rectpack_basic_layout.addRow(self.use_rectpack_algorithm)

        # RectPack旋转设置
        self.rectpack_rotation_enabled = QCheckBox("启用图片旋转")
        self.rectpack_rotation_enabled.setToolTip("启用后，RectPack算法会自动旋转图片以获得更好的排列效果")
        rectpack_basic_layout.addRow(self.rectpack_rotation_enabled)

        # RectPack排序策略
        self.rectpack_sort_strategy = QComboBox()
        self.rectpack_sort_strategy.addItems([
            "按面积排序 (推荐)",
            "按周长排序",
            "按差值排序",
            "按短边排序",
            "按长边排序",
            "按比例排序"
        ])
        self.rectpack_sort_strategy.setToolTip("选择RectPack算法的排序策略")
        rectpack_basic_layout.addRow("排序策略:", self.rectpack_sort_strategy)

        # RectPack装箱算法
        self.rectpack_pack_algorithm = QComboBox()
        self.rectpack_pack_algorithm.addItems([
            "Bottom-Left Fill (推荐)",
            "Best Fit First",
            "Best Bin Fit"
        ])
        self.rectpack_pack_algorithm.setToolTip("选择RectPack算法的装箱策略")
        rectpack_basic_layout.addRow("装箱算法:", self.rectpack_pack_algorithm)

        layout_layout.addWidget(rectpack_basic_group)

        # RectPack算法高级设置组
        rectpack_advanced_group = QGroupBox("RectPack算法高级设置")
        rectpack_advanced_layout = QFormLayout(rectpack_advanced_group)

        # Bin选择策略
        self.rectpack_bin_selection_strategy = QComboBox()
        self.rectpack_bin_selection_strategy.addItems([
            "Best Short Side Fit (推荐)",
            "Best Long Side Fit",
            "Best Area Fit",
            "Bottom Left Rule",
            "Contact Point Rule"
        ])
        self.rectpack_bin_selection_strategy.setToolTip("选择Bin选择策略")
        rectpack_advanced_layout.addRow("Bin选择策略:", self.rectpack_bin_selection_strategy)

        # 分割启发式
        self.rectpack_split_heuristic = QComboBox()
        self.rectpack_split_heuristic.addItems([
            "Shorter Leftover Horizontal (推荐)",
            "Shorter Leftover Vertical",
            "Longer Leftover Horizontal",
            "Longer Leftover Vertical"
        ])
        self.rectpack_split_heuristic.setToolTip("选择分割启发式策略")
        rectpack_advanced_layout.addRow("分割启发式:", self.rectpack_split_heuristic)

        # 自由矩形选择
        self.rectpack_free_rect_choice = QComboBox()
        self.rectpack_free_rect_choice.addItems([
            "Best Short Side Fit (推荐)",
            "Best Long Side Fit",
            "Best Area Fit",
            "Worst Area Fit",
            "Worst Short Side Fit",
            "Worst Long Side Fit"
        ])
        self.rectpack_free_rect_choice.setToolTip("选择自由矩形选择策略")
        rectpack_advanced_layout.addRow("自由矩形选择:", self.rectpack_free_rect_choice)

        layout_layout.addWidget(rectpack_advanced_group)

        # RectPack算法优化设置组
        rectpack_optimization_group = QGroupBox("RectPack算法优化设置")
        rectpack_optimization_layout = QFormLayout(rectpack_optimization_group)

        # 启用优化
        self.rectpack_enable_optimization = QCheckBox("启用利用率优化")
        self.rectpack_enable_optimization.setToolTip("启用后，算法会尝试多种配置以获得最佳利用率")
        rectpack_optimization_layout.addRow(self.rectpack_enable_optimization)

        # 优化迭代次数
        self.rectpack_optimization_iterations = QSpinBox()
        self.rectpack_optimization_iterations.setMinimum(1)
        self.rectpack_optimization_iterations.setMaximum(20)
        self.rectpack_optimization_iterations.setSingleStep(1)
        self.rectpack_optimization_iterations.setToolTip("优化算法的迭代次数，越多越精确但耗时更长")
        rectpack_optimization_layout.addRow("优化迭代次数:", self.rectpack_optimization_iterations)

        # 最小利用率阈值
        self.rectpack_min_utilization_threshold = QDoubleSpinBox()
        self.rectpack_min_utilization_threshold.setMinimum(50.0)
        self.rectpack_min_utilization_threshold.setMaximum(99.0)
        self.rectpack_min_utilization_threshold.setSingleStep(1.0)
        self.rectpack_min_utilization_threshold.setDecimals(1)
        self.rectpack_min_utilization_threshold.setSuffix("%")
        self.rectpack_min_utilization_threshold.setToolTip("最小利用率阈值，低于此值会尝试优化")
        rectpack_optimization_layout.addRow("最小利用率阈值:", self.rectpack_min_utilization_threshold)

        # 旋转惩罚系数
        self.rectpack_rotation_penalty = QDoubleSpinBox()
        self.rectpack_rotation_penalty.setMinimum(0.0)
        self.rectpack_rotation_penalty.setMaximum(1.0)
        self.rectpack_rotation_penalty.setSingleStep(0.01)
        self.rectpack_rotation_penalty.setDecimals(2)
        self.rectpack_rotation_penalty.setToolTip("旋转惩罚系数，越高越不倾向于旋转图片")
        rectpack_optimization_layout.addRow("旋转惩罚系数:", self.rectpack_rotation_penalty)

        # 宽高比偏好
        self.rectpack_aspect_ratio_preference = QDoubleSpinBox()
        self.rectpack_aspect_ratio_preference.setMinimum(0.1)
        self.rectpack_aspect_ratio_preference.setMaximum(10.0)
        self.rectpack_aspect_ratio_preference.setSingleStep(0.1)
        self.rectpack_aspect_ratio_preference.setDecimals(1)
        self.rectpack_aspect_ratio_preference.setToolTip("宽高比偏好：1.0=无偏好，>1偏好横向，<1偏好纵向")
        rectpack_optimization_layout.addRow("宽高比偏好:", self.rectpack_aspect_ratio_preference)

        layout_layout.addWidget(rectpack_optimization_group)

        # 精确查询图案全称开关
        self.exact_pattern_search = QCheckBox("精确查询图案全称")
        self.exact_pattern_search.setToolTip("开启后，仅精确查询'图案全称'字段，不查询'图案'字段")
        layout_layout.addRow(self.exact_pattern_search)

        # 添加说明标签
        description_label = QLabel("开启后，DuckDB查询时，仅精确查询'图案全称'结果，不查询'图案'字段。")
        description_label.setWordWrap(True)
        layout_layout.addRow(description_label)

        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout_layout.addRow(separator)

        # 标准表格模式开关
        self.is_standard_mode = QCheckBox("使用标准表格模式")
        self.is_standard_mode.setToolTip("开启后，使用标准表格模式处理Excel数据；关闭后，使用自定义表格模式")
        layout_layout.addRow(self.is_standard_mode)

        # 添加说明标签
        standard_mode_label = QLabel("开启后，使用标准表格模式处理Excel数据；关闭后，使用自定义表格模式处理Excel数据。")
        standard_mode_label.setWordWrap(True)
        layout_layout.addRow(standard_mode_label)

        # 添加分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.HLine)
        separator2.setFrameShadow(QFrame.Shadow.Sunken)
        layout_layout.addRow(separator2)

        # 模糊查询开关
        self.is_fuzzy_query = QCheckBox("启用模糊查询")
        self.is_fuzzy_query.setToolTip("开启后，使用模糊查询匹配图案名称；关闭后，使用精确匹配")
        layout_layout.addRow(self.is_fuzzy_query)

        # 添加说明标签
        fuzzy_query_label = QLabel("开启后，使用模糊查询匹配图案名称，可以查询到部分匹配的结果；关闭后，仅查询完全匹配的结果。")
        fuzzy_query_label.setWordWrap(True)
        layout_layout.addRow(fuzzy_query_label)

        tab_widget.addTab(layout_tab, "RectPack设置")

        # 添加RectPack性能设置选项卡
        rectpack_performance_tab = QWidget()
        rectpack_performance_layout = QFormLayout(rectpack_performance_tab)

        # RectPack性能参数组
        performance_group = QGroupBox("RectPack性能参数")
        performance_layout = QFormLayout(performance_group)

        # 最大处理时间
        self.rectpack_max_processing_time = QSpinBox()
        self.rectpack_max_processing_time.setMinimum(30)
        self.rectpack_max_processing_time.setMaximum(3600)
        self.rectpack_max_processing_time.setSingleStep(30)
        self.rectpack_max_processing_time.setSuffix(" 秒")
        self.rectpack_max_processing_time.setToolTip("算法最大处理时间，超时会停止优化")
        performance_layout.addRow("最大处理时间:", self.rectpack_max_processing_time)

        # 批处理大小
        self.rectpack_batch_size = QSpinBox()
        self.rectpack_batch_size.setMinimum(10)
        self.rectpack_batch_size.setMaximum(1000)
        self.rectpack_batch_size.setSingleStep(10)
        self.rectpack_batch_size.setToolTip("批处理大小，影响内存使用和处理速度")
        performance_layout.addRow("批处理大小:", self.rectpack_batch_size)

        # 内存限制
        self.rectpack_memory_limit_mb = QSpinBox()
        self.rectpack_memory_limit_mb.setMinimum(256)
        self.rectpack_memory_limit_mb.setMaximum(8192)
        self.rectpack_memory_limit_mb.setSingleStep(256)
        self.rectpack_memory_limit_mb.setSuffix(" MB")
        self.rectpack_memory_limit_mb.setToolTip("内存使用限制")
        performance_layout.addRow("内存限制:", self.rectpack_memory_limit_mb)

        # 启用并行处理
        self.rectpack_enable_parallel = QCheckBox("启用并行处理")
        self.rectpack_enable_parallel.setToolTip("启用后，使用多线程加速处理（实验性功能）")
        performance_layout.addRow(self.rectpack_enable_parallel)

        rectpack_performance_layout.addWidget(performance_group)

        # RectPack调试参数组
        debug_group = QGroupBox("RectPack调试参数")
        debug_layout = QFormLayout(debug_group)

        # 调试模式
        self.rectpack_debug_mode = QCheckBox("启用调试模式")
        self.rectpack_debug_mode.setToolTip("启用后，输出详细的调试信息")
        debug_layout.addRow(self.rectpack_debug_mode)

        # 日志级别
        self.rectpack_log_level = QComboBox()
        self.rectpack_log_level.addItems([
            "无日志",
            "基础日志 (推荐)",
            "详细日志",
            "调试日志"
        ])
        self.rectpack_log_level.setToolTip("选择日志输出级别")
        debug_layout.addRow("日志级别:", self.rectpack_log_level)

        # 保存中间结果
        self.rectpack_save_intermediate_results = QCheckBox("保存中间结果")
        self.rectpack_save_intermediate_results.setToolTip("保存算法执行过程中的中间结果，用于调试")
        debug_layout.addRow(self.rectpack_save_intermediate_results)

        # 启用可视化
        self.rectpack_visualization_enabled = QCheckBox("启用可视化")
        self.rectpack_visualization_enabled.setToolTip("生成布局可视化图片")
        debug_layout.addRow(self.rectpack_visualization_enabled)

        rectpack_performance_layout.addWidget(debug_group)

        tab_widget.addTab(rectpack_performance_tab, "RectPack性能")

        # 注意：已移除Tetris算法设置选项卡，现在使用RectPack算法
        classification_group = QGroupBox("图片分类参数")
        classification_layout = QFormLayout(classification_group)

        # A类图片边长阈值（百分比）
        self.class_a_threshold = QSpinBox()
        self.class_a_threshold.setMinimum(80)
        self.class_a_threshold.setMaximum(100)
        self.class_a_threshold.setSingleStep(1)
        self.class_a_threshold.setSuffix("%")
        self.class_a_threshold.setToolTip("A类图片边长与画布宽度的比例阈值")
        classification_layout.addRow("A类图片边长阈值:", self.class_a_threshold)

        # B类图片边长倍数误差范围（百分比）
        self.class_b_error_range = QSpinBox()
        self.class_b_error_range.setMinimum(1)
        self.class_b_error_range.setMaximum(10)
        self.class_b_error_range.setSingleStep(1)
        self.class_b_error_range.setSuffix("%")
        self.class_b_error_range.setToolTip("B类图片边长倍数与画布宽度的误差范围")
        classification_layout.addRow("B类图片误差范围:", self.class_b_error_range)

        algorithm_layout.addWidget(classification_group)

        # 旋转决策参数组
        rotation_group = QGroupBox("旋转决策参数")
        rotation_layout = QFormLayout(rotation_group)

        # A类图片旋转提升阈值（百分比）
        self.class_a_rotation_threshold = QSpinBox()
        self.class_a_rotation_threshold.setMinimum(5)
        self.class_a_rotation_threshold.setMaximum(50)
        self.class_a_rotation_threshold.setSingleStep(5)
        self.class_a_rotation_threshold.setSuffix("%")
        self.class_a_rotation_threshold.setToolTip("A类图片旋转后利用率提升阈值")
        rotation_layout.addRow("A类旋转提升阈值:", self.class_a_rotation_threshold)

        # B类图片旋转提升阈值（百分比）
        self.class_b_rotation_threshold = QSpinBox()
        self.class_b_rotation_threshold.setMinimum(5)
        self.class_b_rotation_threshold.setMaximum(50)
        self.class_b_rotation_threshold.setSingleStep(5)
        self.class_b_rotation_threshold.setSuffix("%")
        self.class_b_rotation_threshold.setToolTip("B类图片旋转后利用率提升阈值")
        rotation_layout.addRow("B类旋转提升阈值:", self.class_b_rotation_threshold)

        # C类图片旋转提升阈值（百分比）
        self.class_c_rotation_threshold = QSpinBox()
        self.class_c_rotation_threshold.setMinimum(5)
        self.class_c_rotation_threshold.setMaximum(50)
        self.class_c_rotation_threshold.setSingleStep(5)
        self.class_c_rotation_threshold.setSuffix("%")
        self.class_c_rotation_threshold.setToolTip("C类图片旋转后利用率提升阈值")
        rotation_layout.addRow("C类旋转提升阈值:", self.class_c_rotation_threshold)

        # 极端宽高比阈值（比例）
        self.extreme_ratio_threshold = QDoubleSpinBox()
        self.extreme_ratio_threshold.setMinimum(1.5)
        self.extreme_ratio_threshold.setMaximum(5.0)
        self.extreme_ratio_threshold.setSingleStep(0.5)
        self.extreme_ratio_threshold.setDecimals(1)
        self.extreme_ratio_threshold.setToolTip("当图片宽高比超过此值时，考虑旋转")
        rotation_layout.addRow("极端宽高比阈值:", self.extreme_ratio_threshold)

        # 极端宽高比旋转利用率阈值（百分比）
        self.extreme_ratio_utilization = QSpinBox()
        self.extreme_ratio_utilization.setMinimum(30)
        self.extreme_ratio_utilization.setMaximum(80)
        self.extreme_ratio_utilization.setSingleStep(5)
        self.extreme_ratio_utilization.setSuffix("%")
        self.extreme_ratio_utilization.setToolTip("当图片宽高比超过阈值时，旋转后的最低利用率要求")
        rotation_layout.addRow("极端比利用率阈值:", self.extreme_ratio_utilization)

        algorithm_layout.addWidget(rotation_group)

        # 行空隙填充参数组
        gap_filling_group = QGroupBox("行空隙填充参数")
        gap_filling_layout = QFormLayout(gap_filling_group)

        # 行利用率阈值（百分比）
        self.row_utilization_threshold = QSpinBox()
        self.row_utilization_threshold.setMinimum(80)
        self.row_utilization_threshold.setMaximum(100)
        self.row_utilization_threshold.setSingleStep(1)
        self.row_utilization_threshold.setSuffix("%")
        self.row_utilization_threshold.setToolTip("当行利用率达到此值时，认为是最佳选择")
        gap_filling_layout.addRow("行利用率阈值:", self.row_utilization_threshold)

        # C类图片行空隙匹配误差范围（百分比）
        self.class_c_gap_error_range = QSpinBox()
        self.class_c_gap_error_range.setMinimum(1)
        self.class_c_gap_error_range.setMaximum(10)
        self.class_c_gap_error_range.setSingleStep(1)
        self.class_c_gap_error_range.setSuffix("%")
        self.class_c_gap_error_range.setToolTip("C类图片行空隙匹配误差范围")
        gap_filling_layout.addRow("C类空隙误差范围:", self.class_c_gap_error_range)

        # 启用行空隙填充
        self.enable_row_gap_filling = QCheckBox("启用行空隙填充")
        self.enable_row_gap_filling.setToolTip("启用后，天际线算法会尝试填充行空隙，提高水平利用率")
        gap_filling_layout.addRow(self.enable_row_gap_filling)

        algorithm_layout.addWidget(gap_filling_group)

        # C类俄罗斯方块算法高级参数组
        tetris_group = QGroupBox("C类俄罗斯方块算法高级参数")
        tetris_layout = QFormLayout(tetris_group)

        # C类最小利用率提升阈值（百分比）- 只有超过此值才旋转
        self.c_min_utilization_improvement = QSpinBox()
        self.c_min_utilization_improvement.setMinimum(10)
        self.c_min_utilization_improvement.setMaximum(50)
        self.c_min_utilization_improvement.setSingleStep(5)
        self.c_min_utilization_improvement.setSuffix("%")
        self.c_min_utilization_improvement.setToolTip("C类图片旋转后必须至少提高此百分比的利用率才会旋转")
        tetris_layout.addRow("最小利用率提升:", self.c_min_utilization_improvement)

        # C类高利用率阈值（百分比）- 超过此值视为高利用率
        self.c_high_utilization_threshold = QSpinBox()
        self.c_high_utilization_threshold.setMinimum(85)
        self.c_high_utilization_threshold.setMaximum(99)
        self.c_high_utilization_threshold.setSingleStep(1)
        self.c_high_utilization_threshold.setSuffix("%")
        self.c_high_utilization_threshold.setToolTip("超过此值视为高利用率")
        tetris_layout.addRow("高利用率阈值:", self.c_high_utilization_threshold)

        # C类极端宽高比最小值 - 超过此值视为极端宽高比
        self.c_extreme_ratio_min = QDoubleSpinBox()
        self.c_extreme_ratio_min.setMinimum(3.0)
        self.c_extreme_ratio_min.setMaximum(10.0)
        self.c_extreme_ratio_min.setSingleStep(0.5)
        self.c_extreme_ratio_min.setDecimals(1)
        self.c_extreme_ratio_min.setToolTip("宽高比超过此值视为极端宽高比")
        tetris_layout.addRow("极端宽高比最小值:", self.c_extreme_ratio_min)

        # C类水平优先级（百分比）- 越高越优先考虑水平利用率
        self.c_horizontal_priority = QSpinBox()
        self.c_horizontal_priority.setMinimum(50)
        self.c_horizontal_priority.setMaximum(100)
        self.c_horizontal_priority.setSingleStep(5)
        self.c_horizontal_priority.setSuffix("%")
        self.c_horizontal_priority.setToolTip("水平优先级，越高越优先考虑水平利用率")
        tetris_layout.addRow("水平优先级:", self.c_horizontal_priority)

        # C类空隙填充优先级（百分比）- 越高越优先填充现有行空隙
        self.c_gap_filling_priority = QSpinBox()
        self.c_gap_filling_priority.setMinimum(50)
        self.c_gap_filling_priority.setMaximum(100)
        self.c_gap_filling_priority.setSingleStep(5)
        self.c_gap_filling_priority.setSuffix("%")
        self.c_gap_filling_priority.setToolTip("空隙填充优先级，越高越优先填充现有行空隙")
        tetris_layout.addRow("空隙填充优先级:", self.c_gap_filling_priority)

        # C类旋转优先级（百分比）- 越高越倾向于旋转图片
        self.c_rotation_priority = QSpinBox()
        self.c_rotation_priority.setMinimum(50)
        self.c_rotation_priority.setMaximum(100)
        self.c_rotation_priority.setSingleStep(5)
        self.c_rotation_priority.setSuffix("%")
        self.c_rotation_priority.setToolTip("旋转优先级，越高越倾向于旋转图片")
        tetris_layout.addRow("旋转优先级:", self.c_rotation_priority)

        algorithm_layout.addWidget(tetris_group)

        # 画布利用率迭代参数组
        canvas_iteration_group = QGroupBox("画布利用率迭代参数")
        canvas_iteration_layout = QFormLayout(canvas_iteration_group)

        # 是否开启人工审核利用率
        self.is_check_canvas = QCheckBox("开启人工审核利用率")
        self.is_check_canvas.setToolTip("开启后，将在排版过程中进行人工审核利用率")
        canvas_iteration_layout.addRow(self.is_check_canvas)

        # 画布利用率每次迭代个数
        self.canvas_iteration_count = QSpinBox()
        self.canvas_iteration_count.setMinimum(1)
        self.canvas_iteration_count.setMaximum(20)
        self.canvas_iteration_count.setSingleStep(1)
        self.canvas_iteration_count.setToolTip("每次迭代的个数")
        canvas_iteration_layout.addRow("每次迭代个数:", self.canvas_iteration_count)

        # 画布利用率总迭代次数
        self.canvas_iteration_time = QSpinBox()
        self.canvas_iteration_time.setMinimum(1)
        self.canvas_iteration_time.setMaximum(100)
        self.canvas_iteration_time.setSingleStep(1)
        self.canvas_iteration_time.setToolTip("总迭代次数")
        canvas_iteration_layout.addRow("总迭代次数:", self.canvas_iteration_time)

        algorithm_layout.addWidget(canvas_iteration_group)

        # 添加说明标签
        algorithm_description = QLabel("这些参数影响图片分类、旋转决策和行空隙填充策略。谨慎调整这些参数，以免影响排列效果。")
        algorithm_description.setWordWrap(True)
        algorithm_layout.addWidget(algorithm_description)

        tab_widget.addTab(algorithm_tab, "算法设置")

        # 添加Photoshop设置选项卡
        ps_tab = QWidget()
        ps_layout = QFormLayout(ps_tab)

        # 使用Photoshop设置
        self.use_photoshop = QCheckBox("使用Photoshop处理图像")
        ps_layout.addRow(self.use_photoshop)

        # 自动启动Photoshop设置
        self.auto_start_photoshop = QCheckBox("自动启动Photoshop")
        ps_layout.addRow(self.auto_start_photoshop)

        # 保存格式设置
        self.save_format = QComboBox()
        self.save_format.addItems(['TIFF', 'JPEG'])
        ps_layout.addRow("保存格式:", self.save_format)

        # 压缩方式设置
        self.compression = QComboBox()
        self.compression.addItems(['LZW', 'ZIP', 'JPEG'])
        ps_layout.addRow("压缩方式:", self.compression)

        tab_widget.addTab(ps_tab, "Photoshop设置")

        # 添加图库索引选项卡
        db_scan_tab = QWidget()
        db_scan_layout = QFormLayout(db_scan_tab)

        # 图库索引快速模式开关
        self.is_db_scan_fast = QCheckBox("使用快速模式索引图库")
        self.is_db_scan_fast.setToolTip("开启后，索引图库时仅获取文件路径和名称信息，不读取图片内容，提高索引效率")
        db_scan_layout.addRow(self.is_db_scan_fast)

        # 添加说明标签
        db_scan_description = QLabel("开启快速模式后，索引图库时仅获取文件路径和名称信息，不读取图片内容，可以显著提高索引效率，避免处理大图片时的内存问题。")
        db_scan_description.setWordWrap(True)
        db_scan_layout.addRow(db_scan_description)

        tab_widget.addTab(db_scan_tab, "图库索引")

        # 添加测试模式选项卡
        test_mode_tab = QWidget()
        test_mode_layout = QFormLayout(test_mode_tab)

        # 测试模式开关
        self.is_test_mode = QCheckBox("启用测试模式")
        self.is_test_mode.setToolTip("开启后，使用色块替代图片，不启动Photoshop，提高测试效率")
        test_mode_layout.addRow(self.is_test_mode)

        # 添加说明标签
        test_mode_description = QLabel("开启测试模式后，将使用不同颜色的色块替代图片，不启动Photoshop，提高测试效率。")
        test_mode_description.setWordWrap(True)
        test_mode_layout.addRow(test_mode_description)

        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        test_mode_layout.addRow(separator)

        # 缩小模型比率
        self.miniature_ratio = QDoubleSpinBox()
        self.miniature_ratio.setMinimum(0.01)
        self.miniature_ratio.setMaximum(1.0)
        self.miniature_ratio.setSingleStep(0.01)
        self.miniature_ratio.setDecimals(2)
        self.miniature_ratio.setToolTip("缩小模型比率，用于测试模式下生成的图片大小")
        test_mode_layout.addRow("缩小模型比率:", self.miniature_ratio)

        # 添加说明标签
        miniature_ratio_description = QLabel("缩小模型比率用于测试模式下生成的图片大小，例如0.02表示生成的图片为原始大小的2%。")
        miniature_ratio_description.setWordWrap(True)
        test_mode_layout.addRow(miniature_ratio_description)

        # 添加分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.HLine)
        separator2.setFrameShadow(QFrame.Shadow.Sunken)
        test_mode_layout.addRow(separator2)

        # 测试全部数据开关
        self.is_test_all_data = QCheckBox("测试全部数据")
        self.is_test_all_data.setToolTip("开启后，测试表格里的全部图片数据，不检测是否有图片路径")
        test_mode_layout.addRow(self.is_test_all_data)

        # 添加说明标签
        test_all_data_description = QLabel("开启后，测试表格里的全部图片数据，不检测是否有图片路径；关闭后，只测试有图片路径的数据，快速测试结果。")
        test_all_data_description.setWordWrap(True)
        test_mode_layout.addRow(test_all_data_description)

        tab_widget.addTab(test_mode_tab, "测试模式")

        layout.addWidget(tab_widget)

        # 添加按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def _init_canvas_ui(self, canvas_layout):
        """初始化画布设置UI"""
        # 直接使用传入的布局而不是创建新的QGroupBox
        # 最大高度 (厘米)
        self.max_height = QSpinBox()
        self.max_height.setMinimum(100)  # 最小1米
        self.max_height.setMaximum(50000)  # 最大500米
        self.max_height.setSingleStep(100)  # 步长1米
        self.max_height.setSuffix(" cm")  # 单位厘米
        canvas_layout.addRow("最大高度:", self.max_height)

        # PPI (像素每英寸)
        self.ppi = QSpinBox()
        self.ppi.setMinimum(36)  # 最小36 PPI
        self.ppi.setMaximum(600)  # 最大600 PPI
        self.ppi.setSingleStep(6)  # 步长6 PPI
        canvas_layout.addRow("分辨率 (PPI):", self.ppi)

        # 图像间距 (厘米)
        self.image_spacing = QDoubleSpinBox()
        self.image_spacing.setMinimum(0.0)  # 最小0厘米
        self.image_spacing.setMaximum(10.0)  # 最大10厘米
        self.image_spacing.setSingleStep(0.1)  # 步长0.1厘米
        self.image_spacing.setDecimals(1)  # 精度0.1厘米
        self.image_spacing.setSuffix(" cm")  # 单位厘米
        canvas_layout.addRow("图像间距:", self.image_spacing)

        # 水平扩展 (厘米)
        self.horizontal_expansion = QDoubleSpinBox()
        self.horizontal_expansion.setMinimum(0.0)  # 最小0厘米
        self.horizontal_expansion.setMaximum(10.0)  # 最大10厘米
        self.horizontal_expansion.setSingleStep(0.1)  # 步长0.1厘米
        self.horizontal_expansion.setDecimals(1)  # 精度0.1厘米
        self.horizontal_expansion.setSuffix(" cm")  # 单位厘米
        canvas_layout.addRow("水平扩展:", self.horizontal_expansion)

    def load_settings(self):
        """加载设置"""
        try:
            # 画布设置
            canvas_settings = self.config_manager.get_canvas_settings()
            self.max_height.setValue(int(canvas_settings['max_height_cm']))
            self.ppi.setValue(int(canvas_settings['ppi']))
            self.image_spacing.setValue(float(canvas_settings['image_spacing_cm']))
            self.horizontal_expansion.setValue(float(canvas_settings['horizontal_expansion_cm']))

            # RectPack算法设置
            rectpack_settings = self.config_manager.get_rectpack_settings()

            # 基础设置
            self.use_rectpack_algorithm.setChecked(rectpack_settings.get('use_rectpack_algorithm', False))
            self.rectpack_rotation_enabled.setChecked(rectpack_settings.get('rectpack_rotation_enabled', True))
            self.rectpack_sort_strategy.setCurrentIndex(rectpack_settings.get('rectpack_sort_strategy', 0))
            self.rectpack_pack_algorithm.setCurrentIndex(rectpack_settings.get('rectpack_pack_algorithm', 0))

            # 高级设置
            self.rectpack_bin_selection_strategy.setCurrentIndex(rectpack_settings.get('rectpack_bin_selection_strategy', 0))
            self.rectpack_split_heuristic.setCurrentIndex(rectpack_settings.get('rectpack_split_heuristic', 0))
            self.rectpack_free_rect_choice.setCurrentIndex(rectpack_settings.get('rectpack_free_rect_choice', 0))

            # 优化设置
            self.rectpack_enable_optimization.setChecked(rectpack_settings.get('rectpack_enable_optimization', True))
            self.rectpack_optimization_iterations.setValue(rectpack_settings.get('rectpack_optimization_iterations', 5))
            self.rectpack_min_utilization_threshold.setValue(rectpack_settings.get('rectpack_min_utilization_threshold', 85.0))
            self.rectpack_rotation_penalty.setValue(rectpack_settings.get('rectpack_rotation_penalty', 0.05))
            self.rectpack_aspect_ratio_preference.setValue(rectpack_settings.get('rectpack_aspect_ratio_preference', 1.0))

            # 性能设置
            self.rectpack_max_processing_time.setValue(rectpack_settings.get('rectpack_max_processing_time', 300))
            self.rectpack_batch_size.setValue(rectpack_settings.get('rectpack_batch_size', 100))
            self.rectpack_memory_limit_mb.setValue(rectpack_settings.get('rectpack_memory_limit_mb', 1024))
            self.rectpack_enable_parallel.setChecked(rectpack_settings.get('rectpack_enable_parallel', False))

            # 调试设置
            self.rectpack_debug_mode.setChecked(rectpack_settings.get('rectpack_debug_mode', False))
            self.rectpack_log_level.setCurrentIndex(rectpack_settings.get('rectpack_log_level', 1))
            self.rectpack_save_intermediate_results.setChecked(rectpack_settings.get('rectpack_save_intermediate_results', False))
            self.rectpack_visualization_enabled.setChecked(rectpack_settings.get('rectpack_visualization_enabled', False))

            # 排列设置
            # 获取精确查询图案全称设置，默认为关闭
            exact_pattern_search = self.config_manager.get('exact_pattern_search', False)
            self.exact_pattern_search.setChecked(exact_pattern_search)

            # 获取表格模式设置，默认为标准模式
            is_standard_mode = self.config_manager.get('is_standard_mode', True)
            self.is_standard_mode.setChecked(is_standard_mode)

            # 获取模糊查询设置，默认为关闭
            is_fuzzy_query = self.config_manager.get('is_fuzzy_query', False)
            self.is_fuzzy_query.setChecked(is_fuzzy_query)

            # 算法设置
            algorithm_settings = self.config_manager.get_algorithm_settings()

            # 图片分类参数
            self.class_a_threshold.setValue(algorithm_settings['class_a_threshold'])
            self.class_b_error_range.setValue(algorithm_settings['class_b_error_range'])

            # 旋转决策参数
            self.class_a_rotation_threshold.setValue(algorithm_settings['class_a_rotation_threshold'])
            self.class_b_rotation_threshold.setValue(algorithm_settings['class_b_rotation_threshold'])
            self.class_c_rotation_threshold.setValue(algorithm_settings['class_c_rotation_threshold'])
            self.extreme_ratio_threshold.setValue(algorithm_settings['extreme_ratio_threshold'])
            self.extreme_ratio_utilization.setValue(algorithm_settings['extreme_ratio_utilization'])

            # 行空隙填充参数
            self.row_utilization_threshold.setValue(algorithm_settings['row_utilization_threshold'])
            self.class_c_gap_error_range.setValue(algorithm_settings['class_c_gap_error_range'])
            self.enable_row_gap_filling.setChecked(algorithm_settings.get('enable_row_gap_filling', True))

            # C类俄罗斯方块算法高级参数
            self.c_min_utilization_improvement.setValue(algorithm_settings.get('c_min_utilization_improvement', 30))
            self.c_high_utilization_threshold.setValue(algorithm_settings.get('c_high_utilization_threshold', 95))
            self.c_extreme_ratio_min.setValue(algorithm_settings.get('c_extreme_ratio_min', 5.0))
            self.c_horizontal_priority.setValue(algorithm_settings.get('c_horizontal_priority', 80))
            self.c_gap_filling_priority.setValue(algorithm_settings.get('c_gap_filling_priority', 70))
            self.c_rotation_priority.setValue(algorithm_settings.get('c_rotation_priority', 60))

            # 画布利用率迭代参数
            canvas_iteration_params = self.config_manager.get_canvas_iteration_params()
            self.is_check_canvas.setChecked(canvas_iteration_params.get('is_check_canvas', False))
            self.canvas_iteration_count.setValue(canvas_iteration_params.get('canvas_iteration_count', 5))
            self.canvas_iteration_time.setValue(canvas_iteration_params.get('canvas_iteration_time', 5))

            # Photoshop设置
            ps_settings = self.config_manager.get_photoshop_settings()
            self.use_photoshop.setChecked(ps_settings['use_photoshop'])
            self.auto_start_photoshop.setChecked(ps_settings['auto_start_photoshop'])

            # 保存设置 - 找到相应的索引
            save_format = ps_settings['save_format']
            save_format_index = self.save_format.findText(save_format)
            if save_format_index >= 0:
                self.save_format.setCurrentIndex(save_format_index)

            # 压缩设置 - 找到相应的索引
            compression = ps_settings['compression']
            compression_index = self.compression.findText(compression)
            if compression_index >= 0:
                self.compression.setCurrentIndex(compression_index)

            # 图库索引设置
            is_db_scan_fast = self.config_manager.get_db_scan_fast()
            self.is_db_scan_fast.setChecked(is_db_scan_fast)

            # 测试模式设置
            test_mode_settings = self.config_manager.get_test_mode_settings()
            self.is_test_mode.setChecked(test_mode_settings['is_test_mode'])
            self.miniature_ratio.setValue(test_mode_settings['miniature_ratio'])
            self.is_test_all_data.setChecked(test_mode_settings.get('is_test_all_data', False))
        except Exception as e:
            log.error(f"加载设置时出错: {str(e)}")
            # 显示错误消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "加载设置失败", f"无法加载设置: {str(e)}\n将使用默认值。")
            # 使用默认值
            self.max_height.setValue(1000)
            self.ppi.setValue(72)
            self.image_spacing.setValue(1.0)
            self.horizontal_expansion.setValue(0)
            self.use_photoshop.setChecked(True)
            self.auto_start_photoshop.setChecked(True)
            self.save_format.setCurrentText("TIFF")
            self.compression.setCurrentText("LZW")

    def get_settings(self) -> Dict[str, Any]:
        """获取设置值

        Returns:
            设置字典
        """
        return {
            # 画布设置
            'max_height_cm': self.max_height.value(),
            'ppi': self.ppi.value(),
            'image_spacing_cm': self.image_spacing.value(),
            'horizontal_expansion_cm': self.horizontal_expansion.value(),

            # RectPack算法设置
            'use_rectpack_algorithm': self.use_rectpack_algorithm.isChecked(),
            'rectpack_rotation_enabled': self.rectpack_rotation_enabled.isChecked(),
            'rectpack_sort_strategy': self.rectpack_sort_strategy.currentIndex(),
            'rectpack_pack_algorithm': self.rectpack_pack_algorithm.currentIndex(),

            # 排列设置
            'exact_pattern_search': self.exact_pattern_search.isChecked(),
            'is_standard_mode': self.is_standard_mode.isChecked(),
            'is_fuzzy_query': self.is_fuzzy_query.isChecked(),

            # 算法设置 - 图片分类参数
            'class_a_threshold': self.class_a_threshold.value(),
            'class_b_error_range': self.class_b_error_range.value(),

            # 算法设置 - 旋转决策参数
            'class_a_rotation_threshold': self.class_a_rotation_threshold.value(),
            'class_b_rotation_threshold': self.class_b_rotation_threshold.value(),
            'class_c_rotation_threshold': self.class_c_rotation_threshold.value(),
            'extreme_ratio_threshold': self.extreme_ratio_threshold.value(),
            'extreme_ratio_utilization': self.extreme_ratio_utilization.value(),

            # 算法设置 - 行空隙填充参数
            'row_utilization_threshold': self.row_utilization_threshold.value(),
            'class_c_gap_error_range': self.class_c_gap_error_range.value(),
            'enable_row_gap_filling': self.enable_row_gap_filling.isChecked(),

            # 算法设置 - C类俄罗斯方块算法高级参数
            'c_min_utilization_improvement': self.c_min_utilization_improvement.value(),
            'c_high_utilization_threshold': self.c_high_utilization_threshold.value(),
            'c_extreme_ratio_min': self.c_extreme_ratio_min.value(),
            'c_horizontal_priority': self.c_horizontal_priority.value(),
            'c_gap_filling_priority': self.c_gap_filling_priority.value(),
            'c_rotation_priority': self.c_rotation_priority.value(),

            # 画布利用率迭代参数
            'is_check_canvas': self.is_check_canvas.isChecked(),
            'canvas_iteration_count': self.canvas_iteration_count.value(),
            'canvas_iteration_time': self.canvas_iteration_time.value(),

            # Photoshop设置
            'use_photoshop': self.use_photoshop.isChecked(),
            'auto_start_photoshop': self.auto_start_photoshop.isChecked(),
            'save_format': self.save_format.currentText(),
            'compression': self.compression.currentText(),

            # 图库索引设置
            'is_db_scan_fast': self.is_db_scan_fast.isChecked(),

            # 测试模式设置
            'is_test_mode': self.is_test_mode.isChecked(),
            'miniature_ratio': self.miniature_ratio.value(),
            'is_test_all_data': self.is_test_all_data.isChecked()
        }

    def accept(self):
        """确认设置"""
        try:
            # 保存设置，使用save_settings方法
            self.save_settings()
            # 通知主窗口设置已更改
            from PyQt6.QtWidgets import QApplication
            QApplication.processEvents()  # 确保UI更新
            super().accept()
        except Exception as e:
            log.error(f"保存设置时出错: {str(e)}")
            # 显示错误消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "保存设置失败", f"无法保存设置: {str(e)}")
            # 尝试关闭对话框
            super().accept()

    def save_settings(self):
        """保存设置"""
        try:
            # 构建要更新的设置字典
            settings = self.get_settings()
            # 使用配置管理器的update方法批量更新
            success = self.config_manager.update(settings)
            if not success:
                log.warning("设置可能未完全保存")
        except Exception as e:
            log.error(f"保存设置异常: {str(e)}")
            # 尝试单独保存每个设置项
            try:
                # 画布设置
                self.config_manager.set('max_height_cm', self.max_height.value())
                self.config_manager.set('ppi', self.ppi.value())
                self.config_manager.set('image_spacing_cm', self.image_spacing.value())
                self.config_manager.set('horizontal_expansion_cm', self.horizontal_expansion.value())

                # RectPack算法设置
                rectpack_settings = {
                    # 基础设置
                    'use_rectpack_algorithm': self.use_rectpack_algorithm.isChecked(),
                    'rectpack_rotation_enabled': self.rectpack_rotation_enabled.isChecked(),
                    'rectpack_sort_strategy': self.rectpack_sort_strategy.currentIndex(),
                    'rectpack_pack_algorithm': self.rectpack_pack_algorithm.currentIndex(),

                    # 高级设置
                    'rectpack_bin_selection_strategy': self.rectpack_bin_selection_strategy.currentIndex(),
                    'rectpack_split_heuristic': self.rectpack_split_heuristic.currentIndex(),
                    'rectpack_free_rect_choice': self.rectpack_free_rect_choice.currentIndex(),

                    # 优化设置
                    'rectpack_enable_optimization': self.rectpack_enable_optimization.isChecked(),
                    'rectpack_optimization_iterations': self.rectpack_optimization_iterations.value(),
                    'rectpack_min_utilization_threshold': self.rectpack_min_utilization_threshold.value(),
                    'rectpack_rotation_penalty': self.rectpack_rotation_penalty.value(),
                    'rectpack_aspect_ratio_preference': self.rectpack_aspect_ratio_preference.value(),

                    # 性能设置
                    'rectpack_max_processing_time': self.rectpack_max_processing_time.value(),
                    'rectpack_batch_size': self.rectpack_batch_size.value(),
                    'rectpack_memory_limit_mb': self.rectpack_memory_limit_mb.value(),
                    'rectpack_enable_parallel': self.rectpack_enable_parallel.isChecked(),

                    # 调试设置
                    'rectpack_debug_mode': self.rectpack_debug_mode.isChecked(),
                    'rectpack_log_level': self.rectpack_log_level.currentIndex(),
                    'rectpack_save_intermediate_results': self.rectpack_save_intermediate_results.isChecked(),
                    'rectpack_visualization_enabled': self.rectpack_visualization_enabled.isChecked()
                }
                self.config_manager.set_rectpack_settings(rectpack_settings)

                # 排列设置
                self.config_manager.set('exact_pattern_search', self.exact_pattern_search.isChecked())
                self.config_manager.set('is_standard_mode', self.is_standard_mode.isChecked())
                self.config_manager.set('is_fuzzy_query', self.is_fuzzy_query.isChecked())

                # Photoshop设置
                self.config_manager.set('use_photoshop', self.use_photoshop.isChecked())
                self.config_manager.set('auto_start_photoshop', self.auto_start_photoshop.isChecked())
                self.config_manager.set('save_format', self.save_format.currentText())
                self.config_manager.set('compression', self.compression.currentText())

                # 图库索引设置
                self.config_manager.set('is_db_scan_fast', self.is_db_scan_fast.isChecked())

                # 测试模式设置
                self.config_manager.set('is_test_mode', self.is_test_mode.isChecked())
                self.config_manager.set('miniature_ratio', self.miniature_ratio.value())
                self.config_manager.set('is_test_all_data', self.is_test_all_data.isChecked())
            except Exception as e2:
                log.error(f"备用保存设置方法也失败: {str(e2)}")
                raise
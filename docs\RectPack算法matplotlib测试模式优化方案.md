# RectPack算法matplotlib测试模式优化方案

## 需求分析

### 当前问题
1. RectPack算法测试模式使用PIL绘制，与参考测试脚本不一致
2. 单位转换复杂（cm到px），增加了不必要的复杂性
3. 缺少统一的测试模式标准

### 目标需求
1. **统一使用matplotlib绘制**：参照 `tests/test_rectpack_real_data.py` 的方式
2. **统一使用px单位**：避免cm到px的转换复杂性
3. **水平拓展支持**：容器宽度 = 基础宽度 + 水平拓展
4. **模块化设计**：分步骤、分阶段、拆分更小的函数

### 示例场景
- 图片尺寸：120x60 cm → 120x60 px
- 容器：宽200cm，水平拓展2cm → 宽200px，水平拓展2px
- 实际容器宽度：202px
- 最大高度：5000cm → 5000px

## 设计方案

### 第一阶段：数据准备模块
1. **单位统一模块**：cm数据转换为px数据
2. **容器配置模块**：处理水平拓展逻辑
3. **数据验证模块**：确保数据有效性

### 第二阶段：matplotlib绘制模块
1. **画布创建模块**：创建matplotlib画布
2. **图片绘制模块**：绘制彩色矩形
3. **标签添加模块**：添加图片标识和信息
4. **统计显示模块**：显示布局统计信息

### 第三阶段：集成优化模块
1. **接口统一模块**：与现有RectPack排列器集成
2. **性能优化模块**：提高绘制效率
3. **错误处理模块**：完善异常处理

### 第四阶段：测试验证模块
1. **功能测试模块**：验证各个功能模块
2. **性能测试模块**：测试绘制性能
3. **对比测试模块**：与PIL模式对比

## 技术实现

### 核心函数设计

```python
# 第一阶段：数据准备
def convert_cm_to_px_data(cm_data: List[Dict]) -> List[Dict]
def create_container_config_with_expansion(base_width: int, expansion: int, max_height: int) -> Dict
def validate_px_data(px_data: List[Dict]) -> bool

# 第二阶段：matplotlib绘制
def create_matplotlib_canvas(container_config: Dict) -> Tuple[Figure, Axes]
def draw_image_rectangles(ax: Axes, placed_images: List[Dict], colors: List) -> None
def add_image_labels(ax: Axes, placed_images: List[Dict]) -> None
def add_statistics_display(ax: Axes, stats: Dict, container_config: Dict) -> None

# 第三阶段：集成优化
def create_matplotlib_test_mode(arranger: RectPackArranger, **kwargs) -> bool
def save_matplotlib_result(fig: Figure, output_path: str) -> bool
def generate_matplotlib_documentation(stats: Dict, output_path: str) -> bool

# 第四阶段：测试验证
def test_matplotlib_mode_basic() -> bool
def test_matplotlib_mode_performance() -> bool
def compare_matplotlib_vs_pil() -> Dict
```

### 颜色方案
- 使用matplotlib的tab20颜色方案
- 确保相邻图片颜色不同
- 支持旋转图片的特殊标记

### 性能优化
- 批量绘制矩形
- 优化字体渲染
- 减少不必要的计算

## 实现计划

### 步骤1：创建数据准备模块
- 实现单位转换函数
- 实现容器配置函数
- 实现数据验证函数

### 步骤2：创建matplotlib绘制模块
- 实现画布创建函数
- 实现图片绘制函数
- 实现标签添加函数
- 实现统计显示函数

### 步骤3：集成到RectPack排列器
- 添加matplotlib测试模式方法
- 更新现有接口
- 保持向后兼容性

### 步骤4：测试和验证
- 创建测试脚本
- 验证功能正确性
- 测试性能表现
- 对比不同模式

## 预期效果

1. **统一的测试体验**：与参考测试脚本一致的matplotlib绘制
2. **简化的单位处理**：统一使用px，避免转换复杂性
3. **灵活的容器配置**：支持水平拓展等高级配置
4. **优秀的性能表现**：快速的绘制和保存
5. **完善的文档支持**：详细的测试报告和说明

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版Tetris优化器模块

集成所有优化功能：
1. 改进图片排列逻辑，最小化空隙
2. 优化填充算法，识别并填充不规则空白区域
3. 实现更智能的图片旋转策略
4. 增强图片分组排列功能
5. 降低整体画布高度
6. 添加画布利用率计算和实时反馈机制
7. 优化底部区域的填充逻辑
8. 实现多次优化尝试功能
"""

import logging
import copy
import time
from typing import List, Dict, Any, Tuple, Optional

# 导入优化器模块
from utils.tetris_optimizer_extension import TetrisOptimizerExtension
from utils.tetris_optimizer_extension2 import TetrisOptimizerExtension2
from utils.canvas_utilization_analyzer import CanvasUtilizationAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("TetrisEnhancedOptimizer")

class TetrisEnhancedOptimizer:
    """
    增强版Tetris优化器，集成所有优化功能
    
    特性：
    1. 改进图片排列逻辑，最小化空隙
    2. 优化填充算法，识别并填充不规则空白区域
    3. 实现更智能的图片旋转策略
    4. 增强图片分组排列功能
    5. 降低整体画布高度
    6. 添加画布利用率计算和实时反馈机制
    7. 优化底部区域的填充逻辑
    8. 实现多次优化尝试功能
    """
    
    def __init__(self, tetris_packer=None):
        """
        初始化增强版Tetris优化器
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        
        # 创建优化器实例
        self.optimizer_extension = TetrisOptimizerExtension(tetris_packer)
        self.optimizer_extension2 = TetrisOptimizerExtension2(tetris_packer)
        self.utilization_analyzer = CanvasUtilizationAnalyzer(tetris_packer)
        
        # 优化参数
        self.optimization_level = 2  # 优化级别：1=基础，2=中等，3=高级
        self.max_attempts = 5  # 最大尝试次数
        self.improvement_threshold = 0.05  # 改进阈值（5%）
        
        log.info("增强版Tetris优化器初始化完成")
    
    def optimize(self, tetris_packer=None, optimization_level: int = None) -> Dict[str, Any]:
        """
        执行优化
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            optimization_level: 优化级别，如果为None则使用默认级别
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return {'success': False, 'message': "未提供Tetris算法实例"}
        
        # 使用指定的优化级别或默认级别
        level = optimization_level or self.optimization_level
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 记录原始布局和利用率
            original_layout = packer.placed_images.copy()
            original_utilization = packer.get_utilization()
            
            log.info(f"开始优化，原始利用率: {original_utilization:.4f}, 优化级别: {level}")
            
            # 根据优化级别执行不同的优化策略
            if level == 1:
                # 基础优化：单次布局优化
                success = self.optimizer_extension.optimize_layout(packer, iterations=2)
                message = "基础优化完成"
            elif level == 2:
                # 中等优化：多次布局优化
                success = self.optimizer_extension2.optimize_layout_with_multiple_attempts(packer, max_attempts=3)
                message = "中等优化完成"
            else:
                # 高级优化：多次布局优化 + 参数调优
                success = self.optimizer_extension2.optimize_layout_with_multiple_attempts(packer, max_attempts=self.max_attempts)
                message = "高级优化完成"
            
            # 计算优化后的利用率
            new_utilization = packer.get_utilization()
            
            # 计算改进百分比
            improvement_percentage = (new_utilization - original_utilization) / original_utilization * 100
            
            # 获取优化建议
            suggestions = self.utilization_analyzer.get_optimization_suggestions(packer)
            
            # 记录结束时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 返回优化结果
            return {
                'success': success,
                'message': message,
                'original_utilization': original_utilization,
                'new_utilization': new_utilization,
                'improvement_percentage': improvement_percentage,
                'execution_time': execution_time,
                'suggestions': suggestions
            }
            
        except Exception as e:
            log.error(f"优化失败: {str(e)}")
            # 恢复原始布局
            if 'original_layout' in locals():
                packer.placed_images = original_layout
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            return {'success': False, 'message': f"优化失败: {str(e)}"}
    
    def get_utilization_report(self, tetris_packer=None) -> Dict[str, Any]:
        """
        获取利用率报告
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            
        Returns:
            Dict[str, Any]: 利用率报告
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return {'overall': 0.0, 'message': "未提供Tetris算法实例"}
        
        try:
            # 计算利用率
            utilization = self.utilization_analyzer.calculate_utilization(packer)
            
            # 获取优化建议
            suggestions = self.utilization_analyzer.get_optimization_suggestions(packer)
            
            # 返回报告
            return {
                'utilization': utilization,
                'suggestions': suggestions,
                'message': "利用率报告生成成功"
            }
            
        except Exception as e:
            log.error(f"生成利用率报告失败: {str(e)}")
            return {'overall': 0.0, 'message': f"生成利用率报告失败: {str(e)}"}
            
# 使用示例
# optimizer = TetrisEnhancedOptimizer(tetris_packer)
# result = optimizer.optimize(optimization_level=3)
# print(f"优化结果: 利用率从 {result['original_utilization']:.4f} 提高到 {result['new_utilization']:.4f}, 提升 {result['improvement_percentage']:.2f}%")

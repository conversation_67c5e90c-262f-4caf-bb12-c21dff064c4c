#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack算法坐标流转深度测试脚本

测试从RectPack算法生成坐标到PS放置图片的完整流程，
确保坐标在每个环节都正确传递

作者: RectPack算法测试团队
日期: 2024-12-19
版本: 坐标流转测试版
"""

import sys
import os
import time
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rectpack_coordinate_generation():
    """
    测试RectPack算法坐标生成
    """
    print("🔍 第一步：测试RectPack算法坐标生成")
    print("=" * 80)
    
    try:
        from core.unified_image_arranger import UnifiedImageArranger
        
        # 创建测试数据
        test_patterns = [
            {
                'pattern_name': 'test1', 
                'width_cm': 12, 
                'height_cm': 8, 
                'quantity': 1,
                'path': '/fake/path/test1.jpg',
                'index': 0,
                'row_number': 1
            },
            {
                'pattern_name': 'test2', 
                'width_cm': 8, 
                'height_cm': 6, 
                'quantity': 1,
                'path': '/fake/path/test2.jpg',
                'index': 1,
                'row_number': 2
            },
            {
                'pattern_name': 'test3', 
                'width_cm': 10, 
                'height_cm': 7, 
                'quantity': 1,
                'path': '/fake/path/test3.jpg',
                'index': 2,
                'row_number': 3
            },
        ]
        
        print(f"📋 测试数据: {len(test_patterns)} 张图片")
        for i, pattern in enumerate(test_patterns, 1):
            print(f"  {i}. {pattern['pattern_name']}: {pattern['width_cm']}x{pattern['height_cm']}cm")
        
        # 初始化UnifiedImageArranger
        arranger = UnifiedImageArranger()
        
        # 设置画布参数（使用较小的尺寸便于测试）
        canvas_width_px = 205  # 205px
        max_height_px = 500    # 500px
        image_spacing_px = 1   # 1px
        ppi = 72
        
        print(f"\n🎨 画布参数:")
        print(f"  宽度: {canvas_width_px}px")
        print(f"  最大高度: {max_height_px}px")
        print(f"  图片间距: {image_spacing_px}px")
        print(f"  PPI: {ppi}")
        
        # 初始化排列器
        arranger.initialize(
            canvas_width_px=canvas_width_px,
            max_height_px=max_height_px,
            image_spacing_px=image_spacing_px,
            ppi=ppi
        )
        
        print(f"\n🔧 开始RectPack算法排列...")
        
        # 执行排列
        arranged_images = arranger.arrange_images(test_patterns)
        
        print(f"\n📊 RectPack算法输出结果:")
        print(f"  输入图片数: {len(test_patterns)}")
        print(f"  成功排列数: {len(arranged_images)}")
        
        if arranged_images:
            print(f"\n📍 详细坐标信息:")
            for i, img in enumerate(arranged_images, 1):
                x = img.get('x', 0)
                y = img.get('y', 0)
                width = img.get('width', 0)
                height = img.get('height', 0)
                rotated = img.get('need_rotation', False)
                name = img.get('name', f'Image_{i}')
                
                print(f"  {i}. {name}:")
                print(f"     RectPack输出坐标: ({x}, {y})")
                print(f"     RectPack输出尺寸: {width}x{height}px")
                print(f"     RectPack输出旋转: {'是' if rotated else '否'}")
                
                # 验证坐标合理性
                if x < 0 or y < 0:
                    print(f"     ❌ 错误: 坐标为负数")
                elif width <= 0 or height <= 0:
                    print(f"     ❌ 错误: 尺寸无效")
                elif x + width > canvas_width_px:
                    print(f"     ❌ 错误: 超出画布宽度 ({x + width} > {canvas_width_px})")
                elif y + height > max_height_px:
                    print(f"     ❌ 错误: 超出画布高度 ({y + height} > {max_height_px})")
                else:
                    print(f"     ✅ 坐标和尺寸合理")
        
        return arranged_images
        
    except Exception as e:
        print(f"❌ RectPack算法坐标生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_rectpack_layout_worker_processing(arranged_images):
    """
    测试RectPackLayoutWorker的数据处理
    """
    print("\n🔍 第二步：测试RectPackLayoutWorker数据处理")
    print("=" * 80)
    
    try:
        print(f"📋 输入arranged_images: {len(arranged_images)} 张图片")
        
        # 模拟RectPackLayoutWorker的数据处理逻辑
        processed_images = []
        
        for i, image_info in enumerate(arranged_images):
            print(f"\n  处理图片 {i+1}: {image_info.get('name', 'Unknown')}")
            
            # 提取数据（完全按照RectPackLayoutWorker的逻辑）
            image_path = image_info.get('path', '')
            x = image_info.get('x', 0)
            y = image_info.get('y', 0)
            width = image_info.get('width', 0)
            height = image_info.get('height', 0)
            need_rotation = image_info.get('need_rotation', False) or image_info.get('rotated', False)
            image_name = image_info.get('name', os.path.basename(image_path))
            image_class = image_info.get('image_class', 'C')
            
            print(f"    提取的数据:")
            print(f"      image_path: {image_path}")
            print(f"      x: {x}, y: {y}")
            print(f"      width: {width}, height: {height}")
            print(f"      need_rotation: {need_rotation}")
            print(f"      image_name: {image_name}")
            print(f"      image_class: {image_class}")
            
            # 创建place_info（完全按照RectPackLayoutWorker的逻辑）
            place_info = {
                'image_path': image_path,
                'x': x,
                'y': y,
                'width': width,
                'height': height,
                'rotated': need_rotation,
                'name': image_name,
                'image_class': image_class
            }
            
            print(f"    生成的place_info:")
            print(f"      {place_info}")
            
            # 验证数据完整性
            if x != image_info.get('x', 0) or y != image_info.get('y', 0):
                print(f"      ❌ 坐标传递错误")
            elif width != image_info.get('width', 0) or height != image_info.get('height', 0):
                print(f"      ❌ 尺寸传递错误")
            else:
                print(f"      ✅ 数据传递正确")
            
            processed_images.append(place_info)
        
        return processed_images
        
    except Exception as e:
        print(f"❌ RectPackLayoutWorker数据处理测试失败: {str(e)}")
        return []

def test_image_processor_processing(processed_images):
    """
    测试image_processor的数据处理
    """
    print("\n🔍 第三步：测试image_processor数据处理")
    print("=" * 80)
    
    try:
        from utils.image_processor import PhotoshopImageProcessor
        import inspect
        
        print(f"📋 输入processed_images: {len(processed_images)} 张图片")
        
        # 检查place_image方法的参数提取逻辑
        processor = PhotoshopImageProcessor()
        method_source = inspect.getsource(processor.place_image)
        
        print(f"\n🔧 image_processor.place_image方法分析:")
        
        # 模拟image_processor.place_image的数据提取逻辑
        for i, place_info in enumerate(processed_images):
            print(f"\n  处理图片 {i+1}: {place_info.get('name', 'Unknown')}")
            
            # 按照image_processor.place_image的逻辑提取数据
            image_path = place_info.get('image_path', '')
            x = place_info.get('x', 0)
            y = place_info.get('y', 0)
            width = place_info.get('width', 0)
            height = place_info.get('height', 0)
            rotated = place_info.get('rotated', False)
            image_name = place_info.get('name', os.path.basename(image_path))
            
            print(f"    image_processor提取的数据:")
            print(f"      image_path: {image_path}")
            print(f"      x: {x}, y: {y}")
            print(f"      width: {width}, height: {height}")
            print(f"      rotated: {rotated}")
            print(f"      image_name: {image_name}")
            
            # 验证数据是否与输入一致
            if (x == place_info.get('x', 0) and 
                y == place_info.get('y', 0) and
                width == place_info.get('width', 0) and
                height == place_info.get('height', 0)):
                print(f"      ✅ image_processor数据提取正确")
            else:
                print(f"      ❌ image_processor数据提取错误")
            
            # 模拟传递给PhotoshopHelper的参数
            ps_params = {
                'image_path': image_path,
                'x': x,
                'y': y,
                'width': width,
                'height': height,
                'rotation': 90 if rotated else 0,
                'image_name': image_name
            }
            
            print(f"    传递给PhotoshopHelper的参数:")
            print(f"      {ps_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ image_processor数据处理测试失败: {str(e)}")
        return False

def test_photoshop_helper_processing():
    """
    测试PhotoshopHelper的参数处理
    """
    print("\n🔍 第四步：测试PhotoshopHelper参数处理")
    print("=" * 80)
    
    try:
        from utils.photoshop_helper import PhotoshopHelper
        import inspect
        
        # 检查place_image方法的参数处理
        method_source = inspect.getsource(PhotoshopHelper.place_image)
        method_signature = inspect.signature(PhotoshopHelper.place_image)
        
        print(f"📋 PhotoshopHelper.place_image方法签名:")
        print(f"  {method_signature}")
        
        print(f"\n🔧 参数处理逻辑分析:")
        
        # 检查关键的参数处理逻辑
        checks = [
            ("支持像素坐标", "x is None or y is None" in method_source),
            ("支持厘米坐标", "x_cm" in method_source and "y_cm" in method_source),
            ("图片尺寸调整", "resizeImage" in method_source),
            ("坐标定位", "translate" in method_source),
            ("旋转处理", "rotateCanvas" in method_source),
            ("JavaScript坐标计算", "moveX = {x} - bounds[0].value" in method_source)
        ]
        
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")
        
        # 模拟参数传递
        test_params = [
            {'x': 0, 'y': 0, 'width': 120, 'height': 80, 'rotation': 0},
            {'x': 125, 'y': 0, 'width': 80, 'height': 60, 'rotation': 0},
            {'x': 0, 'y': 85, 'width': 100, 'height': 70, 'rotation': 90}
        ]
        
        print(f"\n📊 模拟参数传递测试:")
        for i, params in enumerate(test_params, 1):
            print(f"  图片 {i}: 坐标({params['x']},{params['y']}) 尺寸({params['width']}x{params['height']}) 旋转({params['rotation']}°)")
            
            # 检查参数合理性
            if params['x'] < 0 or params['y'] < 0:
                print(f"    ❌ 坐标为负数")
            elif params['width'] <= 0 or params['height'] <= 0:
                print(f"    ❌ 尺寸无效")
            else:
                print(f"    ✅ 参数合理")
        
        return True
        
    except Exception as e:
        print(f"❌ PhotoshopHelper参数处理测试失败: {str(e)}")
        return False

def analyze_coordinate_flow_issues():
    """
    分析坐标流转中的潜在问题
    """
    print("\n🔍 第五步：分析坐标流转中的潜在问题")
    print("=" * 80)
    
    issues = []
    
    try:
        # 问题1: 单位转换问题
        print("📋 检查单位转换问题:")
        print("  RectPack算法输出: 像素坐标")
        print("  RectPackLayoutWorker: 直接传递像素坐标")
        print("  image_processor: 直接传递像素坐标")
        print("  PhotoshopHelper: 接收像素坐标")
        print("  ✅ 单位转换链路正常")
        
        # 问题2: 坐标系统问题
        print("\n📋 检查坐标系统问题:")
        print("  RectPack算法: 左上角为原点(0,0)")
        print("  Photoshop: 左上角为原点(0,0)")
        print("  ✅ 坐标系统一致")
        
        # 问题3: 图片尺寸调整问题
        print("\n📋 检查图片尺寸调整问题:")
        from utils.photoshop_helper import PhotoshopHelper
        import inspect
        
        method_source = inspect.getsource(PhotoshopHelper.place_image)
        
        if "size_needs_adjustment" in method_source:
            print("  ✅ PhotoshopHelper已优化，只在必要时调整尺寸")
        elif "resizeImage" in method_source:
            issues.append({
                'type': '图片尺寸强制调整',
                'description': 'PhotoshopHelper可能强制调整所有图片尺寸，覆盖RectPack算法的精确计算',
                'severity': '高',
                'solution': '优化resizeImage调用逻辑，只在必要时调整'
            })
            print("  ⚠️ PhotoshopHelper可能强制调整所有图片尺寸")
        else:
            print("  ✅ 未发现图片尺寸强制调整问题")
        
        # 问题4: 旋转处理问题
        print("\n📋 检查旋转处理问题:")
        if "rotateCanvas" in method_source:
            print("  ✅ 支持图片旋转")
            if "rotated_width" in method_source and "rotated_height" in method_source:
                print("  ✅ 旋转后重新获取尺寸")
            else:
                issues.append({
                    'type': '旋转后尺寸处理',
                    'description': '旋转后可能没有正确处理图片尺寸',
                    'severity': '中',
                    'solution': '旋转后重新获取图片尺寸'
                })
                print("  ⚠️ 旋转后可能没有正确处理图片尺寸")
        else:
            print("  ❌ 不支持图片旋转")
        
        # 问题5: 坐标计算问题
        print("\n📋 检查坐标计算问题:")
        if "moveX = {x} - bounds[0].value" in method_source:
            print("  ✅ 使用相对坐标计算")
        else:
            issues.append({
                'type': '坐标计算方式',
                'description': '可能使用了不正确的坐标计算方式',
                'severity': '高',
                'solution': '使用相对坐标计算: moveX = target_x - current_x'
            })
            print("  ❌ 坐标计算方式可能不正确")
        
        print(f"\n📊 发现 {len(issues)} 个潜在问题:")
        
        for i, issue in enumerate(issues, 1):
            print(f"\n  问题 {i}: {issue['type']}")
            print(f"    描述: {issue['description']}")
            print(f"    严重程度: {issue['severity']}")
            print(f"    建议解决方案: {issue['solution']}")
        
        if not issues:
            print(f"  ✅ 未发现明显的坐标流转问题")
        
        return issues
        
    except Exception as e:
        print(f"❌ 坐标流转问题分析失败: {str(e)}")
        return []

def main():
    """
    主测试函数
    """
    print("🔬 RectPack算法坐标流转深度测试")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证RectPack算法正确生成坐标")
    print("2. 🔍 验证RectPackLayoutWorker正确处理坐标")
    print("3. 🔍 验证image_processor正确传递坐标")
    print("4. 🔍 验证PhotoshopHelper正确使用坐标")
    print("5. 🔍 分析坐标流转中的潜在问题")
    print("=" * 100)
    
    # 执行测试
    results = []
    
    # 测试1: RectPack算法坐标生成
    arranged_images = test_rectpack_coordinate_generation()
    results.append(("RectPack算法坐标生成", len(arranged_images) > 0))
    
    if arranged_images:
        # 测试2: RectPackLayoutWorker数据处理
        processed_images = test_rectpack_layout_worker_processing(arranged_images)
        results.append(("RectPackLayoutWorker数据处理", len(processed_images) > 0))
        
        if processed_images:
            # 测试3: image_processor数据处理
            result3 = test_image_processor_processing(processed_images)
            results.append(("image_processor数据处理", result3))
    else:
        results.append(("RectPackLayoutWorker数据处理", False))
        results.append(("image_processor数据处理", False))
    
    # 测试4: PhotoshopHelper参数处理
    result4 = test_photoshop_helper_processing()
    results.append(("PhotoshopHelper参数处理", result4))
    
    # 分析5: 坐标流转问题
    issues = analyze_coordinate_flow_issues()
    results.append(("坐标流转问题分析", len(issues) == 0))
    
    # 输出测试结果
    print("\n" + "=" * 100)
    print("📊 坐标流转测试结果汇总:")
    print("=" * 100)
    
    passed_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n📈 总体结果: {passed_count}/{len(results)} 项测试通过")
    
    if passed_count == len(results):
        print("🎉 所有测试通过！坐标流转正常！")
        print("\n✅ 结论: RectPack算法的坐标能够正确传递到PS")
        print("✅ 如果PS中图片依次排列，问题可能在于:")
        print("  1. 图片文件本身的问题")
        print("  2. PS环境的问题")
        print("  3. 实际运行时的异常情况")
    else:
        print("⚠️ 发现问题，需要进一步修复。")
    
    # 输出修复建议
    if issues:
        print("\n💡 修复建议:")
        for i, issue in enumerate(issues, 1):
            if issue['severity'] == '高':
                print(f"  🔥 优先修复: {issue['type']} - {issue['solution']}")
            elif issue['severity'] == '中':
                print(f"  ⚠️ 建议修复: {issue['type']} - {issue['solution']}")
            else:
                print(f"  💡 可选修复: {issue['type']} - {issue['solution']}")
    
    print("\n🔧 下一步行动:")
    print("1. 如果测试显示坐标流转正常，建议在实际PS环境中进行测试")
    print("2. 检查实际图片文件是否存在问题")
    print("3. 验证PS环境是否正常工作")
    print("4. 添加更详细的日志输出，跟踪实际运行时的坐标传递")
    
    return passed_count == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

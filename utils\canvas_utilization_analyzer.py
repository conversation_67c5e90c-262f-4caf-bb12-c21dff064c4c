#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
画布利用率分析器模块

提供画布利用率计算和实时反馈功能：
1. 计算画布整体利用率
2. 计算行利用率
3. 计算区域利用率
4. 提供实时反馈和优化建议
"""

import logging
import time
from typing import List, Dict, Any, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("CanvasUtilizationAnalyzer")

class CanvasUtilizationAnalyzer:
    """
    画布利用率分析器，提供利用率计算和实时反馈功能
    
    特性：
    1. 计算画布整体利用率
    2. 计算行利用率
    3. 计算区域利用率
    4. 提供实时反馈和优化建议
    """
    
    def __init__(self, tetris_packer=None):
        """
        初始化画布利用率分析器
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        self.utilization_history = []  # 利用率历史记录
        self.last_update_time = time.time()  # 上次更新时间
        self.update_interval = 1.0  # 更新间隔（秒）
        log.info("画布利用率分析器初始化完成")
    
    def calculate_utilization(self, tetris_packer=None) -> Dict[str, Any]:
        """
        计算画布利用率
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            
        Returns:
            Dict[str, Any]: 利用率信息
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return {'overall': 0.0, 'rows': {}, 'regions': {}}
        
        try:
            # 计算整体利用率
            overall_utilization = packer.get_utilization()
            
            # 计算行利用率
            row_utilization = packer.get_horizontal_utilization()
            
            # 计算区域利用率
            region_utilization = self._calculate_region_utilization(packer)
            
            # 记录利用率历史
            current_time = time.time()
            if current_time - self.last_update_time >= self.update_interval:
                self.utilization_history.append({
                    'time': current_time,
                    'overall': overall_utilization,
                    'max_row': max(row_utilization.values()) if row_utilization else 0.0,
                    'min_row': min(row_utilization.values()) if row_utilization else 0.0,
                    'avg_row': sum(row_utilization.values()) / len(row_utilization) if row_utilization else 0.0
                })
                self.last_update_time = current_time
            
            # 返回利用率信息
            return {
                'overall': overall_utilization,
                'rows': row_utilization,
                'regions': region_utilization
            }
            
        except Exception as e:
            log.error(f"计算画布利用率失败: {str(e)}")
            return {'overall': 0.0, 'rows': {}, 'regions': {}}
    
    def _calculate_region_utilization(self, packer) -> Dict[str, float]:
        """
        计算区域利用率
        
        Args:
            packer: Tetris算法实例
            
        Returns:
            Dict[str, float]: 区域利用率
        """
        if not packer.placed_images:
            return {'top': 0.0, 'middle': 0.0, 'bottom': 0.0}
        
        # 获取画布高度
        canvas_height = packer.get_max_height()
        if canvas_height <= 0:
            return {'top': 0.0, 'middle': 0.0, 'bottom': 0.0}
        
        # 定义区域
        top_region = (0, canvas_height * 0.33)
        middle_region = (canvas_height * 0.33, canvas_height * 0.67)
        bottom_region = (canvas_height * 0.67, canvas_height)
        
        # 计算区域面积
        top_area = packer.container_width * (top_region[1] - top_region[0])
        middle_area = packer.container_width * (middle_region[1] - middle_region[0])
        bottom_area = packer.container_width * (bottom_region[1] - bottom_region[0])
        
        # 计算区域已占用面积
        top_occupied = 0
        middle_occupied = 0
        bottom_occupied = 0
        
        for img in packer.placed_images:
            img_top = img['y']
            img_bottom = img['y'] + img['height']
            img_width = img['width']
            
            # 计算与各区域的交集
            # 顶部区域
            if img_bottom > top_region[0] and img_top < top_region[1]:
                intersection_top = max(img_top, top_region[0])
                intersection_bottom = min(img_bottom, top_region[1])
                top_occupied += img_width * (intersection_bottom - intersection_top)
            
            # 中部区域
            if img_bottom > middle_region[0] and img_top < middle_region[1]:
                intersection_top = max(img_top, middle_region[0])
                intersection_bottom = min(img_bottom, middle_region[1])
                middle_occupied += img_width * (intersection_bottom - intersection_top)
            
            # 底部区域
            if img_bottom > bottom_region[0] and img_top < bottom_region[1]:
                intersection_top = max(img_top, bottom_region[0])
                intersection_bottom = min(img_bottom, bottom_region[1])
                bottom_occupied += img_width * (intersection_bottom - intersection_top)
        
        # 计算区域利用率
        top_utilization = top_occupied / top_area if top_area > 0 else 0.0
        middle_utilization = middle_occupied / middle_area if middle_area > 0 else 0.0
        bottom_utilization = bottom_occupied / bottom_area if bottom_area > 0 else 0.0
        
        return {
            'top': top_utilization,
            'middle': middle_utilization,
            'bottom': bottom_utilization
        }
    
    def get_optimization_suggestions(self, tetris_packer=None) -> List[str]:
        """
        获取优化建议
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            
        Returns:
            List[str]: 优化建议列表
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return ["未提供Tetris算法实例，无法生成优化建议"]
        
        try:
            # 计算利用率
            utilization = self.calculate_utilization(packer)
            
            # 生成优化建议
            suggestions = []
            
            # 整体利用率建议
            overall = utilization['overall']
            if overall < 0.7:
                suggestions.append(f"整体利用率较低 ({overall:.2%})，建议尝试多次优化尝试功能")
            
            # 区域利用率建议
            regions = utilization['regions']
            if regions['bottom'] < 0.6:
                suggestions.append(f"底部区域利用率较低 ({regions['bottom']:.2%})，建议优化底部填充逻辑")
            
            if regions['top'] > regions['bottom'] * 1.5:
                suggestions.append("顶部区域利用率明显高于底部，建议优化图片排序策略")
            
            # 行利用率建议
            rows = utilization['rows']
            if rows:
                avg_row_util = sum(rows.values()) / len(rows)
                if avg_row_util < 0.8:
                    suggestions.append(f"平均行利用率较低 ({avg_row_util:.2%})，建议增加水平优先级")
            
            # 如果没有具体建议，给出一般性建议
            if not suggestions:
                suggestions.append("当前布局已经较为优化，可以尝试增加旋转优先级以进一步提高利用率")
            
            return suggestions
            
        except Exception as e:
            log.error(f"生成优化建议失败: {str(e)}")
            return ["生成优化建议失败，请检查日志"]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试唯一ID生成器

验证唯一ID生成器的各个功能模块

作者: PS画布修复团队
日期: 2024-12-19
版本: 第二阶段测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_session_manager():
    """测试会话管理器"""
    print("🧪 测试会话管理器")
    print("=" * 60)
    
    try:
        from core.unique_id_generator import SessionManager
        
        # 创建会话管理器
        session_mgr = SessionManager()
        
        print(f"📋 会话管理器测试:")
        print(f"  会话ID: {session_mgr.session_id}")
        print(f"  开始时间: {session_mgr.start_time}")
        
        # 获取会话信息
        session_info = session_mgr.get_session_info()
        print(f"  会话信息: {session_info}")
        
        # 验证会话ID格式
        if len(session_mgr.session_id) == 15 and '_' in session_mgr.session_id:
            print(f"  ✅ 会话ID格式正确")
            return True
        else:
            print(f"  ❌ 会话ID格式错误")
            return False
        
    except Exception as e:
        print(f"❌ 会话管理器测试失败: {str(e)}")
        return False

def test_content_hasher():
    """测试内容哈希生成器"""
    print("\n🧪 测试内容哈希生成器")
    print("=" * 60)
    
    try:
        from core.unique_id_generator import ContentHasher
        
        # 测试图片哈希
        test_images = [
            {'name': 'image1.jpg', 'path': '/path/to/image1.jpg', 'width': 120, 'height': 80},
            {'name': 'image1.jpg', 'path': '/path/to/folder2/image1.jpg', 'width': 100, 'height': 60},
            {'name': 'photo.png', 'path': '/path/to/photo.png', 'width': 150, 'height': 100}
        ]
        
        print(f"📋 内容哈希测试:")
        
        hasher = ContentHasher()
        hashes = []
        
        for i, img in enumerate(test_images, 1):
            img_hash = hasher.generate_image_hash(img)
            path_hash = hasher.generate_path_hash(img['path'])
            
            print(f"  图片 {i}: {img['name']}")
            print(f"    内容哈希: {img_hash}")
            print(f"    路径哈希: {path_hash}")
            
            hashes.append(img_hash)
        
        # 验证哈希唯一性
        unique_hashes = set(hashes)
        if len(unique_hashes) == len(hashes):
            print(f"  ✅ 所有哈希值都是唯一的")
            return True
        else:
            print(f"  ⚠️ 发现哈希冲突: {len(hashes)} 个图片，{len(unique_hashes)} 个唯一哈希")
            return True  # 哈希冲突是可能的，不算失败
        
    except Exception as e:
        print(f"❌ 内容哈希生成器测试失败: {str(e)}")
        return False

def test_sequence_counter():
    """测试序号计数器"""
    print("\n🧪 测试序号计数器")
    print("=" * 60)
    
    try:
        from core.unique_id_generator import SequenceCounter
        
        counter = SequenceCounter()
        
        print(f"📋 序号计数器测试:")
        
        # 测试序号生成
        numbers = []
        for i in range(5):
            num = counter.get_next_number()
            numbers.append(num)
            print(f"  生成序号 {i+1}: {num}")
        
        # 验证序号递增
        is_ascending = all(numbers[i] < numbers[i+1] for i in range(len(numbers)-1))
        if is_ascending:
            print(f"  ✅ 序号递增正确")
        else:
            print(f"  ❌ 序号递增错误")
            return False
        
        # 测试预留序号
        reserved = counter.reserve_number(100)
        if reserved:
            print(f"  ✅ 序号预留成功: 100")
        else:
            print(f"  ❌ 序号预留失败")
            return False
        
        # 获取统计信息
        stats = counter.get_statistics()
        print(f"  统计信息: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 序号计数器测试失败: {str(e)}")
        return False

def test_layer_name_formatter():
    """测试图层名称格式化器"""
    print("\n🧪 测试图层名称格式化器")
    print("=" * 60)
    
    try:
        from core.unique_id_generator import LayerNameFormatter
        
        formatter = LayerNameFormatter()
        
        print(f"📋 图层名称格式化测试:")
        
        # 测试用例
        test_cases = [
            {
                'unique_id': 'RP_20241219_001_a1b2',
                'original_name': 'image1.jpg',
                'expected_prefix': 'RP_20241219_001_a1b2_image1'
            },
            {
                'unique_id': 'RP_20241219_002_c3d4',
                'original_name': 'very_long_image_name_that_might_exceed_limits.png',
                'expected_prefix': 'RP_20241219_002_c3d4_'
            },
            {
                'unique_id': 'RP_20241219_003_e5f6',
                'original_name': 'normal.jpg',
                'expected_prefix': 'RP_20241219_003_e5f6_normal'
            }
        ]
        
        all_passed = True
        
        for i, test in enumerate(test_cases, 1):
            layer_name = formatter.format_layer_name(
                test['unique_id'], 
                test['original_name']
            )
            
            is_valid = formatter.validate_layer_name(layer_name)
            
            print(f"  测试 {i}:")
            print(f"    原始名称: {test['original_name']}")
            print(f"    图层名称: {layer_name}")
            print(f"    有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
            
            if not is_valid:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 图层名称格式化器测试失败: {str(e)}")
        return False

def test_unique_id_generator():
    """测试唯一ID生成器主类"""
    print("\n🧪 测试唯一ID生成器主类")
    print("=" * 60)
    
    try:
        from core.unique_id_generator import RectPackUniqueIDGenerator
        
        generator = RectPackUniqueIDGenerator()
        
        print(f"📋 唯一ID生成器测试:")
        
        # 测试图片数据
        test_images = [
            {'name': 'image1.jpg', 'path': '/path/to/image1.jpg', 'width': 120, 'height': 80},
            {'name': 'image1.jpg', 'path': '/path/to/folder2/image1.jpg', 'width': 100, 'height': 60},
            {'name': 'photo.png', 'path': '/path/to/photo.png', 'width': 150, 'height': 100},
            {'name': 'design.jpg', 'path': '/path/to/design.jpg', 'width': 200, 'height': 120}
        ]
        
        generated_ids = []
        identities = []
        
        # 生成身份信息
        for i, img in enumerate(test_images, 1):
            identity = generator.create_image_identity(img)
            generated_ids.append(identity.unique_id)
            identities.append(identity)
            
            print(f"  图片 {i}: {img['name']}")
            print(f"    唯一ID: {identity.unique_id}")
            print(f"    图层名称: {identity.layer_name}")
            print(f"    内容哈希: {identity.content_hash}")
        
        # 验证ID唯一性
        unique_ids = set(generated_ids)
        if len(unique_ids) == len(generated_ids):
            print(f"  ✅ 所有ID都是唯一的")
        else:
            print(f"  ❌ 发现ID重复")
            return False
        
        # 测试查找功能
        print(f"\n📋 查找功能测试:")
        
        # 根据ID查找
        first_id = generated_ids[0]
        found_identity = generator.get_identity_by_id(first_id)
        if found_identity:
            print(f"  ✅ 根据ID查找成功: {first_id}")
        else:
            print(f"  ❌ 根据ID查找失败")
            return False
        
        # 根据原始名称查找
        matches = generator.find_identity_by_original_name('image1.jpg')
        print(f"  根据原始名称查找到 {len(matches)} 个匹配项")
        
        # 获取统计信息
        stats = generator.get_statistics()
        print(f"\n📋 统计信息:")
        print(f"  生成数量: {stats['generation_count']}")
        print(f"  冲突数量: {stats['collision_count']}")
        print(f"  唯一ID数量: {stats['unique_ids_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 唯一ID生成器测试失败: {str(e)}")
        return False

def test_image_identity():
    """测试图片身份信息类"""
    print("\n🧪 测试图片身份信息类")
    print("=" * 60)
    
    try:
        from core.unique_id_generator import ImageIdentity
        
        # 创建身份信息
        identity = ImageIdentity(
            unique_id="RP_20241219_001_a1b2",
            layer_name="RP_20241219_001_a1b2_image1",
            original_name="image1.jpg",
            content_hash="a1b2",
            session_id="20241219_120000",
            sequence_number=1
        )
        
        print(f"📋 图片身份信息测试:")
        print(f"  唯一ID: {identity.unique_id}")
        print(f"  图层名称: {identity.layer_name}")
        print(f"  原始名称: {identity.original_name}")
        print(f"  创建时间: {identity.creation_time}")
        
        # 测试转换为字典
        identity_dict = identity.to_dict()
        print(f"  字典转换: {len(identity_dict)} 个字段")
        
        # 验证必要字段
        required_fields = ['unique_id', 'layer_name', 'original_name']
        missing_fields = [field for field in required_fields if field not in identity_dict]
        
        if not missing_fields:
            print(f"  ✅ 所有必要字段都存在")
            return True
        else:
            print(f"  ❌ 缺少字段: {missing_fields}")
            return False
        
    except Exception as e:
        print(f"❌ 图片身份信息测试失败: {str(e)}")
        return False

def generate_unique_id_test_report():
    """生成唯一ID生成器测试报告"""
    print("\n📊 唯一ID生成器测试报告")
    print("=" * 80)
    
    # 执行所有测试
    test_results = {
        'session_manager': test_session_manager(),
        'content_hasher': test_content_hasher(),
        'sequence_counter': test_sequence_counter(),
        'layer_formatter': test_layer_name_formatter(),
        'unique_id_generator': test_unique_id_generator(),
        'image_identity': test_image_identity()
    }
    
    # 统计结果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    print(f"\n📋 测试结果汇总:")
    print(f"  会话管理器: {'✅ 通过' if test_results['session_manager'] else '❌ 失败'}")
    print(f"  内容哈希生成器: {'✅ 通过' if test_results['content_hasher'] else '❌ 失败'}")
    print(f"  序号计数器: {'✅ 通过' if test_results['sequence_counter'] else '❌ 失败'}")
    print(f"  图层名称格式化器: {'✅ 通过' if test_results['layer_formatter'] else '❌ 失败'}")
    print(f"  唯一ID生成器: {'✅ 通过' if test_results['unique_id_generator'] else '❌ 失败'}")
    print(f"  图片身份信息: {'✅ 通过' if test_results['image_identity'] else '❌ 失败'}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print(f"🎉 唯一ID生成器测试全部通过！")
        print(f"✅ 可以进入下一步：集成到RectPack算法")
        
        # 功能总结
        print(f"\n🎯 功能验证总结:")
        print(f"  1. ✅ 会话管理: 生成唯一会话ID")
        print(f"  2. ✅ 内容哈希: 基于图片信息生成哈希")
        print(f"  3. ✅ 序号计数: 递增序号生成")
        print(f"  4. ✅ 图层命名: 格式化和验证图层名称")
        print(f"  5. ✅ ID生成: 生成全局唯一ID")
        print(f"  6. ✅ 身份管理: 完整的图片身份信息")
        
    else:
        print(f"⚠️ 唯一ID生成器测试存在问题，需要修复")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🧪 唯一ID生成器功能测试")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证会话管理器功能")
    print("2. 🔍 验证内容哈希生成器")
    print("3. 🔍 验证序号计数器")
    print("4. 🔍 验证图层名称格式化器")
    print("5. 🔍 验证唯一ID生成器主类")
    print("6. 🔍 验证图片身份信息类")
    print("=" * 100)
    
    # 执行测试
    success = generate_unique_id_test_report()
    
    if success:
        print(f"\n🎯 下一步行动:")
        print(f"1. ✅ 集成唯一ID生成器到RectPackArranger")
        print(f"2. ✅ 修改PhotoshopHelper使用唯一图层名称")
        print(f"3. ✅ 测试图片覆盖问题是否解决")
        print(f"4. ✅ 验证空白问题是否改善")
    else:
        print(f"\n🔧 需要修复的问题:")
        print(f"1. 检查失败的测试模块")
        print(f"2. 修复相关功能")
        print(f"3. 重新运行测试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

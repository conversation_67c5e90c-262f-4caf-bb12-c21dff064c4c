#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片处理器模块，提供统一的图片处理接口，分离测试模式和正式模式的实现
"""

import os
import sys
import random
import logging
import datetime
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Tuple, Optional, Union

# 导入常量
from utils.constants import constants, get_constant

# 配置日志
from utils.log_config import get_logger
log = get_logger("ImageProcessor")

# 导入PIL库，用于测试模式下生成色块图片
try:
    from PIL import Image, ImageDraw, ImageFont
except ImportError:
    # 如果没有安装PIL库，尝试安装
    import subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pillow"])
        from PIL import Image, ImageDraw, ImageFont
    except Exception as e:
        log.error(f"安装PIL库失败: {str(e)}")
        # 定义一个空的Image类，避免导入错误
        class Image:
            @staticmethod
            def new(*args, **kwargs):
                return None

        class ImageDraw:
            @staticmethod
            def Draw(*args, **kwargs):
                return None

        class ImageFont:
            @staticmethod
            def truetype(*args, **kwargs):
                return None


class ImageProcessorBase(ABC):
    """图片处理器基类，定义了图片处理的通用接口"""

    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化处理器

        Args:
            config: 配置参数

        Returns:
            是否初始化成功
        """
        pass

    @abstractmethod
    def create_canvas(self, width: int, height: int, name: str, ppi: int = 72) -> bool:
        """创建画布

        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 画布名称
            ppi: 画布分辨率（像素/英寸）

        Returns:
            是否创建成功
        """
        pass

    @abstractmethod
    def place_image(self, image_info: Dict[str, Any]) -> bool:
        """放置图片

        Args:
            image_info: 图片信息，包含路径、位置、尺寸等

        Returns:
            是否放置成功
        """
        pass

    @abstractmethod
    def save_canvas(self, output_path: str) -> bool:
        """保存画布

        Args:
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        pass

    @abstractmethod
    def close_canvas(self) -> bool:
        """关闭画布

        Returns:
            是否关闭成功
        """
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """清理资源

        Returns:
            是否清理成功
        """
        pass

    @abstractmethod
    def generate_description(self, output_path: str, images_info: List[Dict[str, Any]],
                            canvas_info: Dict[str, Any]) -> bool:
        """生成画布说明文档

        Args:
            output_path: 输出路径
            images_info: 图片信息列表
            canvas_info: 画布信息

        Returns:
            是否生成成功
        """
        pass


class PhotoshopImageProcessor(ImageProcessorBase):
    """Photoshop图片处理器，使用Photoshop API处理图片"""

    def __init__(self):
        """初始化Photoshop图片处理器"""
        self.initialized = False
        self.ps_helper = None

    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化处理器

        Args:
            config: 配置参数

        Returns:
            是否初始化成功
        """
        try:
            # 导入PhotoshopHelper
            from utils.photoshop_helper import PhotoshopHelper
            self.ps_helper = PhotoshopHelper

            # 检查Photoshop是否可用
            is_available, message = self.ps_helper.check_photoshop()
            if not is_available:
                log.error(f"Photoshop不可用: {message}")
                return False

            self.initialized = True
            log.info("Photoshop图片处理器初始化成功")
            return True
        except Exception as e:
            log.error(f"初始化Photoshop图片处理器失败: {str(e)}")
            return False

    def create_canvas(self, width: int, height: int, name: str, ppi: int = 72) -> bool:
        """创建画布

        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 画布名称
            ppi: 画布分辨率（像素/英寸）

        Returns:
            是否创建成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            # 检查画布尺寸是否超过最大限制
            max_width_px = get_constant('PS_MAX_CANVAS_WIDTH_PX', 35433)  # 默认3米
            max_height_px = get_constant('PS_MAX_CANVAS_HEIGHT_PX', 590551)  # 默认50米

            if width > max_width_px:
                log.warning(f"画布宽度 {width}px 超过最大限制 {max_width_px}px，将被截断")
                width = max_width_px

            if height > max_height_px:
                log.warning(f"画布高度 {height}px 超过最大限制 {max_height_px}px，将被截断")
                height = max_height_px

            # 使用默认PPI如果未指定
            if ppi <= 0:
                ppi = get_constant('PS_DEFAULT_PPI', 72)

            return self.ps_helper.create_canvas(width, height, name, ppi)
        except Exception as e:
            log.error(f"创建画布失败: {str(e)}")
            return False

    def place_image(self, image_info: Dict[str, Any]) -> bool:
        """放置图片 - 严格按照RectPack算法的布局结果

        Args:
            image_info: 图片信息，包含路径、位置、尺寸等

        Returns:
            是否放置成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            # 获取RectPack算法的精确布局信息
            image_path = image_info.get('image_path', '')
            x = image_info.get('x', 0)  # RectPack算法计算的精确坐标
            y = image_info.get('y', 0)
            width = image_info.get('width', 0)  # RectPack算法计算的精确尺寸
            height = image_info.get('height', 0)
            rotated = image_info.get('rotated', False)  # RectPack的旋转决策
            image_name = image_info.get('name', os.path.basename(image_path))

            # 记录RectPack布局信息
            log.info(f"RectPack布局: {image_name} 位置({x},{y}) 尺寸({width}x{height}) 旋转={rotated}")

            # 使用RectPack算法的精确布局参数调用PhotoshopHelper
            # 重要：直接传递RectPack算法计算出的精确坐标和尺寸，不做任何修改
            rotation_angle = 90 if rotated else 0

            # 获取PPI设置
            ppi = 72  # 默认PPI
            try:
                from utils.config_manager_duckdb import ConfigManagerDuckDB
                config_manager = ConfigManagerDuckDB()
                canvas_settings = config_manager.get_canvas_settings()
                ppi = canvas_settings.get('ppi', 72)
            except:
                pass  # 使用默认值

            success = self.ps_helper.place_image(
                image_path=image_path,
                x=x,  # RectPack算法的精确坐标，不做任何转换
                y=y,  # RectPack算法的精确坐标，不做任何转换
                width=width,  # RectPack算法的精确尺寸，不做任何转换
                height=height,  # RectPack算法的精确尺寸，不做任何转换
                rotation=rotation_angle,  # RectPack的旋转决策
                ppi=ppi  # 从配置读取的PPI
            )

            if success:
                log.info(f"RectPack布局成功: {image_name}")
            else:
                log.error(f"RectPack布局失败: {image_name}")

            return success

        except Exception as e:
            log.error(f"放置图片失败: {str(e)}")
            return False

    def save_canvas(self, output_path: str) -> bool:
        """保存画布

        Args:
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 根据文件扩展名确定保存格式
            ext = os.path.splitext(output_path)[1].lower()
            format = 'JPEG' if ext in ['.jpg', '.jpeg'] else 'TIFF'

            return self.ps_helper.save_document(output_path, format)
        except Exception as e:
            log.error(f"保存画布失败: {str(e)}")
            return False

    def close_canvas(self, save: bool = False) -> bool:
        """关闭画布以节省内存

        Args:
            save: 是否在关闭前保存

        Returns:
            是否关闭成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            log.info(f"关闭画布以节省内存 (保存: {save})")
            return self.ps_helper.close_document(save=save)
        except Exception as e:
            log.error(f"关闭画布失赅: {str(e)}")
            return False

    def cleanup(self) -> bool:
        """清理资源

        Returns:
            是否清理成功
        """
        if not self.initialized:
            return True  # 未初始化，无需清理

        try:
            return self.ps_helper.safe_cleanup_resources()
        except Exception as e:
            log.error(f"清理资源失败: {str(e)}")
            return False

    def generate_description(self, output_path: str, images_info: List[Dict[str, Any]],
                            canvas_info: Dict[str, Any]) -> bool:
        """生成画布说明文档

        Args:
            output_path: 输出路径
            images_info: 图片信息列表
            canvas_info: 画布信息

        Returns:
            是否生成成功
        """
        try:
            # 从 TIFF 文件路径生成说明文档路径
            tiff_filename = os.path.basename(output_path)
            base_name = os.path.splitext(tiff_filename)[0]  # 去掉.tif扩展名
            output_dir = os.path.dirname(output_path)
            doc_path = os.path.join(output_dir, f"{base_name}_说明.md")

            log.info(f"生成RectPack算法TIFF说明文档: {doc_path}")

            # 确保输出目录存在
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 提取画布信息 - 完全参照tetris算法的数据结构
            material_name = canvas_info.get('material_name', '')
            canvas_sequence = canvas_info.get('canvas_sequence', 0)
            canvas_width_m = canvas_info.get('canvas_width_m', 0)
            canvas_width_px = canvas_info.get('canvas_width_px', 0)
            canvas_height = canvas_info.get('canvas_height', 0)
            horizontal_expansion_cm = canvas_info.get('horizontal_expansion_cm', 0)
            max_height_cm = canvas_info.get('max_height_cm', 0)
            ppi = canvas_info.get('ppi', 72)
            generation_time = canvas_info.get('generation_time', '')

            # 计算画布高度（厘米）
            canvas_height_cm = canvas_height/ppi*2.54

            # 统计信息
            total_images = len(images_info)
            class_a_count = len([img for img in images_info if img.get('image_class') == 'A'])
            class_b_count = len([img for img in images_info if img.get('image_class') == 'B'])
            class_c_count = len([img for img in images_info if img.get('image_class') == 'C'])
            rotated_images = len([img for img in images_info if img.get('need_rotation', False) or img.get('rotated', False)])

            # 计算利用率
            if canvas_width_px > 0 and canvas_height > 0:
                total_area = canvas_width_px * canvas_height
                used_area = sum([img.get('width', 0) * img.get('height', 0) for img in images_info])
                utilization = used_area / total_area if total_area > 0 else 0
            else:
                utilization = 0

            # 获取当前时间
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 生成说明文档内容 - 完全参照tetris算法的格式
            with open(doc_path, 'w', encoding='utf-8') as f:
                # 标题和基本信息
                f.write(f"# {material_name}-{canvas_sequence} 正式环境说明文档\n")
                f.write(f"生成时间: {current_time}\n")
                f.write(f"材质名称: {material_name}\n")
                f.write(f"画布序号: {canvas_sequence}\n")
                f.write(f"画布宽度: {canvas_width_m:.2f}米 ({int(canvas_width_m*100)}厘米)\n")
                f.write(f"画布高度: {canvas_height_cm:.2f} 厘米 ({canvas_height} 像素)\n")
                f.write(f"水平拓展: {horizontal_expansion_cm} 厘米\n")
                f.write(f"最大高度限制: {max_height_cm} 厘米\n")

                # 添加缩小模型比率和测试全部数据（正式环境中不适用，但保持格式一致）
                f.write(f"缩小模型比率: 1.0\n")
                f.write(f"测试全部数据: 否\n")

                # 添加利用率统计信息
                f.write("利用率统计\n")
                f.write(f"画布利用率: {utilization*100:.2f}%\n")
                f.write(f"旋转图片比例: {rotated_images/total_images*100:.2f}% ({rotated_images}/{total_images})\n")

                # 添加图片统计
                f.write("## 图片统计\n")
                f.write(f"总图片数: {total_images}\n")
                f.write(f"A类图片(宽幅类): {class_a_count} ({class_a_count/total_images*100:.2f}%)\n")
                f.write(f"B类图片(宽幅约束类): {class_b_count} ({class_b_count/total_images*100:.2f}%)\n")
                f.write(f"C类图片(俄罗斯方块类): {class_c_count} ({class_c_count/total_images*100:.2f}%)\n")

                # 添加图片排列信息
                f.write("## 图片排列信息\n")
                f.write(f"{'序号':<5}{'名称':<30}{'分类':<5}{'位置(x,y)':<15}{'尺寸(宽x高)':<15}{'表格宽-高':<12}{'旋转':<5}\n\n")

                # 按Y坐标排序，使得输出更有序
                sorted_images = sorted(images_info, key=lambda img: (img.get('y', 0), img.get('x', 0)))

                for i, img in enumerate(sorted_images):
                    name = img.get('name', '未命名')
                    image_class = img.get('image_class', 'C')
                    x = img.get('x', 0)
                    y = img.get('y', 0)
                    width = img.get('width', 0)
                    height = img.get('height', 0)
                    need_rotation = img.get('need_rotation', False) or img.get('rotated', False)
                    rotated = '是' if need_rotation else '否'

                    # 获取表格里的宽、高数据（厘米）
                    table_width_cm = img.get('table_width_cm', 0)
                    table_height_cm = img.get('table_height_cm', 0)

                    # 如果没有表格数据，尝试从像素尺寸计算
                    if table_width_cm == 0 and ppi > 0:
                        table_width_cm = width / ppi * 2.54
                    if table_height_cm == 0 and ppi > 0:
                        table_height_cm = height / ppi * 2.54

                    # 格式化表格宽高
                    table_cm_size = f"{int(table_width_cm)}-{int(table_height_cm)}"

                    f.write(f"{i+1:<5}{name[:28]:<30}{image_class:<5}({x},{y}){'':>5}({width}x{height}){'':>3}{table_cm_size:<12}{rotated:<5}\n")

                # 添加色块说明
                f.write("## 色块说明\n")
                f.write("- 红色: A类图片（宽幅类）\n")
                f.write("- 绿色: B类图片（宽幅约束类）\n")
                f.write("- 蓝色: C类图片（俄罗斯方块类）\n")

            log.info(f"RectPack算法TIFF说明文档生成成功: {os.path.basename(doc_path)}")
            return True
        except Exception as e:
            log.error(f"生成RectPack算法TIFF说明文档失败: {str(e)}")
            return False


class TestModeImageProcessor(ImageProcessorBase):
    """测试模式图片处理器，使用PIL库生成色块图片"""

    def __init__(self):
        """初始化测试模式图片处理器"""
        self.initialized = False
        self.canvas = None
        self.draw = None
        self.config = {}
        self.miniature_ratio = 0.02  # 默认缩小比率
        self.is_test_all_data = False  # 默认不测试全部数据
        self.canvas_width = 0
        self.canvas_height = 0
        self.canvas_name = ""

    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化处理器

        Args:
            config: 配置参数，包含miniature_ratio和is_test_all_data

        Returns:
            是否初始化成功
        """
        try:
            # 保存配置
            self.config = config

            # 提取测试模式参数
            self.miniature_ratio = config.get('miniature_ratio', 0.02)
            self.is_test_all_data = config.get('is_test_all_data', False)

            # 检查PIL库是否可用
            try:
                from PIL import Image, ImageDraw, ImageFont
                self.initialized = True
                log.info(f"测试模式图片处理器初始化成功，缩小比率: {self.miniature_ratio}, 测试全部数据: {self.is_test_all_data}")
                return True
            except ImportError:
                log.error("PIL库不可用，测试模式无法正常工作")
                return False
        except Exception as e:
            log.error(f"初始化测试模式图片处理器失败: {str(e)}")
            return False

    def create_canvas(self, width: int, height: int, name: str, ppi: int = 72) -> bool:
        """创建画布

        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 画布名称
            ppi: 画布分辨率（像素/英寸）

        Returns:
            是否创建成功
        """
        if not self.initialized:
            log.error("测试模式图片处理器未初始化")
            return False

        try:
            # 检查画布尺寸是否超过最大限制
            max_width_px = get_constant('PS_MAX_CANVAS_WIDTH_PX', 35433)  # 默认3米
            max_height_px = get_constant('PS_MAX_CANVAS_HEIGHT_PX', 590551)  # 默认50米

            if width > max_width_px:
                log.warning(f"画布宽度 {width}px 超过最大限制 {max_width_px}px，将被截断")
                width = max_width_px

            if height > max_height_px:
                log.warning(f"画布高度 {height}px 超过最大限制 {max_height_px}px，将被截断")
                height = max_height_px

            # 应用缩小比率
            scaled_width = int(width * self.miniature_ratio)
            scaled_height = int(height * self.miniature_ratio)

            # 测试模式下限制画布大小，防止内存溢出
            MAX_TEST_CANVAS_HEIGHT = 30000  # 最大高度限制为30000像素
            MAX_TEST_CANVAS_WIDTH = 20000   # 最大宽度限制为20000像素

            # 检查画布尺寸是否过大
            if scaled_height > MAX_TEST_CANVAS_HEIGHT or scaled_width > MAX_TEST_CANVAS_WIDTH:
                log.warning(f"画布尺寸过大 ({scaled_width}x{scaled_height})，将进行进一步缩放")

                # 计算缩放比例
                scale_factor = min(
                    MAX_TEST_CANVAS_HEIGHT / scaled_height if scaled_height > 0 else 1,
                    MAX_TEST_CANVAS_WIDTH / scaled_width if scaled_width > 0 else 1
                )

                # 缩放画布尺寸
                scaled_width = int(scaled_width * scale_factor)
                scaled_height = int(scaled_height * scale_factor)

                log.info(f"画布已缩放: 原始尺寸={width}x{height}, 缩放比例={self.miniature_ratio*scale_factor:.6f}, 新尺寸={scaled_width}x{scaled_height}")
            else:
                log.info(f"画布已缩放: 原始尺寸={width}x{height}, 缩放比例={self.miniature_ratio:.6f}, 新尺寸={scaled_width}x{scaled_height}")

            # 创建画布 - 明确使用RGB模式
            from PIL import Image, ImageDraw
            self.canvas = Image.new('RGB', (scaled_width, scaled_height), (255, 255, 255))
            self.draw = ImageDraw.Draw(self.canvas)

            # 保存画布信息
            self.canvas_width = width  # 保存原始宽度
            self.canvas_height = height  # 保存原始高度
            self.canvas_name = name

            log.info(f"测试模式画布 {name} 创建成功，尺寸: {scaled_width}x{scaled_height}")
            return True
        except Exception as e:
            log.error(f"创建测试模式画布失败: {str(e)}")
            return False

    def place_image(self, image_info: Dict[str, Any]) -> bool:
        """放置图片

        Args:
            image_info: 图片信息，包含路径、位置、尺寸等

        Returns:
            是否放置成功
        """
        if not self.initialized or self.canvas is None or self.draw is None:
            log.error("测试模式画布未创建")
            return False

        try:
            # 提取图片信息
            image_path = image_info.get('image_path', '')
            image_name = image_info.get('name', os.path.basename(image_path))
            x = image_info.get('x', 0)
            y = image_info.get('y', 0)
            width = image_info.get('width', 0)
            height = image_info.get('height', 0)
            rotated = image_info.get('rotated', False)
            image_class = image_info.get('image_class', 'C')

            # 记录旋转信息
            if rotated:
                log.info(f"测试模式处理旋转图片: {image_name}, 尺寸: {width}x{height}，已包含旋转后的正确尺寸")

            # 应用缩小比率
            scaled_x = int(x * self.miniature_ratio)
            scaled_y = int(y * self.miniature_ratio)
            scaled_width = int(width * self.miniature_ratio)
            scaled_height = int(height * self.miniature_ratio)

            # 确保坐标和尺寸在有效范围内
            canvas_size = self.canvas.size
            scaled_x = max(0, min(scaled_x, canvas_size[0] - 1))
            scaled_y = max(0, min(scaled_y, canvas_size[1] - 1))
            scaled_width = max(1, min(scaled_width, canvas_size[0] - scaled_x))
            scaled_height = max(1, min(scaled_height, canvas_size[1] - scaled_y))

            # 根据图片类别设置颜色
            if image_class == 'A':
                # A类图片 - 红色系
                color = (random.randint(150, 250), random.randint(0, 100), random.randint(0, 100))
            elif image_class == 'B':
                # B类图片 - 绿色系
                color = (random.randint(0, 100), random.randint(150, 250), random.randint(0, 100))
            else:  # C类
                # C类图片 - 蓝色系
                color = (random.randint(0, 100), random.randint(0, 100), random.randint(150, 250))

            # 绘制矩形
            self.draw.rectangle(
                [scaled_x, scaled_y, scaled_x + scaled_width, scaled_y + scaled_height],
                outline=(0, 0, 0),
                fill=color
            )

            # 绘制图片名称
            try:
                from PIL import ImageFont
                try:
                    font = ImageFont.truetype("arial.ttf", 12)
                except:
                    font = ImageFont.load_default()

                # 绘制图片名称 - 只有当矩形足够大时才绘制
                if scaled_width > 30 and scaled_height > 15:
                    display_name = f"{image_name}{'(R)' if rotated else ''}"
                    self.draw.text((scaled_x + 5, scaled_y + 5), display_name, fill=(255, 255, 255), font=font)
            except Exception as font_error:
                log.warning(f"绘制图片名称失败: {str(font_error)}")

            log.info(f"测试模式下放置图片 {image_name} 成功，位置: ({scaled_x}, {scaled_y}), 尺寸: {scaled_width}x{scaled_height}, 类别: {image_class}, 旋转: {rotated}")
            return True
        except Exception as e:
            log.error(f"测试模式下放置图片失败: {str(e)}")
            return False

    def save_canvas(self, output_path: str) -> bool:
        """保存画布

        Args:
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        if not self.initialized or self.canvas is None:
            log.error("测试模式画布未创建")
            return False

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 保存画布
            self.canvas.save(output_path)

            log.info(f"测试模式画布已保存到: {output_path}")
            return True
        except Exception as e:
            log.error(f"保存测试模式画布失败: {str(e)}")
            return False

    def close_canvas(self) -> bool:
        """关闭画布

        Returns:
            是否关闭成功
        """
        if not self.initialized:
            return True  # 未初始化，无需关闭

        try:
            # 释放资源
            self.canvas = None
            self.draw = None

            log.info("测试模式画布已关闭")
            return True
        except Exception as e:
            log.error(f"关闭测试模式画布失败: {str(e)}")
            return False

    def cleanup(self) -> bool:
        """清理资源

        Returns:
            是否清理成功
        """
        if not self.initialized:
            return True  # 未初始化，无需清理

        try:
            # 释放资源
            self.canvas = None
            self.draw = None

            log.info("测试模式资源已清理")
            return True
        except Exception as e:
            log.error(f"清理测试模式资源失败: {str(e)}")
            return False

    def generate_description(self, output_path: str, images_info: List[Dict[str, Any]],
                            canvas_info: Dict[str, Any]) -> bool:
        """生成测试模式说明文档

        Args:
            output_path: 输出路径
            images_info: 图片信息列表
            canvas_info: 画布信息

        Returns:
            是否生成成功
        """
        try:
            # 创建同名的TXT文件路径
            doc_path = os.path.splitext(output_path)[0] + "_说明.txt"
            log.info(f"生成测试模式说明文档: {doc_path}")

            # 确保输出目录存在
            output_dir = os.path.dirname(doc_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 提取画布信息
            canvas_name = canvas_info.get('canvas_name', os.path.basename(output_path))
            material_name = canvas_info.get('material_name', '')
            canvas_sequence = canvas_info.get('canvas_sequence', 0)
            canvas_width_m = canvas_info.get('canvas_width_m', 0)
            canvas_width_px = canvas_info.get('canvas_width_px', 0)
            canvas_height = canvas_info.get('canvas_height', 0)
            horizontal_expansion_cm = canvas_info.get('horizontal_expansion_cm', 0)
            max_height_cm = canvas_info.get('max_height_cm', 0)
            ppi = canvas_info.get('ppi', 72)

            # 计算画布高度（厘米）
            canvas_height_cm = canvas_height/ppi*2.54

            # 统计信息
            total_images = len(images_info)
            class_a_count = len([img for img in images_info if img.get('image_class') == 'A'])
            class_b_count = len([img for img in images_info if img.get('image_class') == 'B'])
            class_c_count = len([img for img in images_info if img.get('image_class') == 'C'])
            rotated_images = len([img for img in images_info if img.get('need_rotation', False) or img.get('rotated', False)])

            # 计算利用率
            if canvas_width_px > 0 and canvas_height > 0:
                total_area = canvas_width_px * canvas_height
                used_area = sum([img.get('width', 0) * img.get('height', 0) for img in images_info])
                utilization = used_area / total_area if total_area > 0 else 0
            else:
                utilization = 0

            # 获取当前时间
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 写入文件
            with open(doc_path, 'w', encoding='utf-8') as f:
                # 标题和基本信息
                f.write(f"# {material_name}-{canvas_sequence} 测试模式说明文档\n")
                f.write(f"生成时间: {current_time}\n")
                f.write(f"材质名称: {material_name}\n")
                f.write(f"画布序号: {canvas_sequence}\n")
                f.write(f"画布宽度: {canvas_width_m:.2f}米 ({int(canvas_width_m*100)}厘米)\n")
                f.write(f"画布高度: {canvas_height_cm:.2f} 厘米 ({canvas_height} 像素)\n")
                f.write(f"水平拓展: {horizontal_expansion_cm} 厘米\n")
                f.write(f"最大高度限制: {max_height_cm} 厘米\n")
                f.write(f"缩小模型比率: {self.miniature_ratio}\n")
                f.write(f"测试全部数据: {'是' if self.is_test_all_data else '否'}\n")

                # 添加利用率统计信息
                f.write("## 利用率统计\n")
                f.write(f"画布利用率: {utilization*100:.2f}%\n")
                f.write(f"旋转图片比例: {rotated_images/total_images*100:.2f}% ({rotated_images}/{total_images})\n")

                # 添加图片统计
                f.write("## 图片统计\n")
                f.write(f"总图片数: {total_images}\n")
                f.write(f"A类图片(宽幅类): {class_a_count} ({class_a_count/total_images*100:.2f}%)\n")
                f.write(f"B类图片(宽幅约束类): {class_b_count} ({class_b_count/total_images*100:.2f}%)\n")
                f.write(f"C类图片(俄罗斯方块类): {class_c_count} ({class_c_count/total_images*100:.2f}%)\n")

                # 添加图片排列信息
                f.write("## 图片排列信息\n")
                f.write(f"{'序号':<5}{'名称':<30}{'分类':<5}{'位置(x,y)':<15}{'尺寸(宽x高)':<15}{'表格宽-高':<12}{'旋转':<5}\n\n")

                # 按Y坐标排序，使得输出更有序
                sorted_images = sorted(images_info, key=lambda img: (img.get('y', 0), img.get('x', 0)))

                for i, img in enumerate(sorted_images):
                    name = img.get('name', '未命名')
                    image_class = img.get('image_class', 'C')
                    x = img.get('x', 0)
                    y = img.get('y', 0)
                    width = img.get('width', 0)
                    height = img.get('height', 0)
                    need_rotation = img.get('need_rotation', False) or img.get('rotated', False)
                    rotated = '是' if need_rotation else '否'

                    # 获取表格里的宽、高数据（厘米）
                    table_width_cm = img.get('table_width_cm', 0)
                    table_height_cm = img.get('table_height_cm', 0)

                    # 如果没有表格数据，尝试从像素尺寸计算
                    if table_width_cm == 0 and ppi > 0:
                        table_width_cm = width / ppi * 2.54
                    if table_height_cm == 0 and ppi > 0:
                        table_height_cm = height / ppi * 2.54

                    # 格式化表格宽高
                    table_cm_size = f"{int(table_width_cm)}-{int(table_height_cm)}"

                    f.write(f"{i+1:<5}{name[:28]:<30}{image_class:<5}({x},{y}){'':>5}({width}x{height}){'':>3}{table_cm_size:<12}{rotated:<5}\n")

                # 添加色块说明
                f.write("## 色块说明\n")
                f.write("- 红色: A类图片（宽幅类）\n")
                f.write("- 绿色: B类图片（宽幅约束类）\n")
                f.write("- 蓝色: C类图片（俄罗斯方块类）\n")

            log.info(f"成功生成测试模式说明文档: {doc_path}")
            return True
        except Exception as e:
            log.error(f"生成测试模式说明文档失败: {str(e)}")
            return False


def get_image_processor(is_test_mode: bool, config: Dict[str, Any] = None) -> ImageProcessorBase:
    """获取图片处理器实例

    Args:
        is_test_mode: 是否测试模式
        config: 配置参数

    Returns:
        图片处理器实例
    """
    if config is None:
        config = {}

    if is_test_mode:
        processor = TestModeImageProcessor()
    else:
        processor = PhotoshopImageProcessor()

    # 初始化处理器
    processor.initialize(config)

    return processor
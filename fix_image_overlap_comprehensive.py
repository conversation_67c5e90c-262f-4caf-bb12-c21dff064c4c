#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片覆盖问题综合修复

彻底解决图片覆盖和空白区域问题

作者: PS画布修复团队
日期: 2024-12-19
版本: 综合修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_overlap_root_causes():
    """分析图片覆盖问题的根本原因"""
    print("🔍 图片覆盖问题根本原因分析")
    print("=" * 80)
    
    root_causes = [
        {
            "id": "RC-001",
            "category": "图层顺序管理",
            "problem": "JavaScript中图层移动逻辑错误",
            "description": "每个新图层都被移动到底层，导致后放置的图片被先放置的图片覆盖",
            "code_location": "utils/photoshop_helper.py:756",
            "current_code": "layer.move(layers[layers.length - 1], ElementPlacement.PLACEAFTER);",
            "impact": "高",
            "fix_priority": 1
        },
        {
            "id": "RC-002", 
            "category": "图层索引计算",
            "problem": "图层索引传递不准确",
            "description": "使用len(self.placed_images)计算索引，但此时placed_images可能还未更新",
            "code_location": "core/rectpack_arranger.py:3020",
            "current_code": "layer_index = len(self.placed_images)",
            "impact": "中",
            "fix_priority": 2
        },
        {
            "id": "RC-003",
            "category": "坐标验证缺失",
            "problem": "缺少坐标冲突检测",
            "description": "没有验证新图片位置是否与已放置图片冲突",
            "code_location": "core/rectpack_arranger.py:place_image",
            "current_code": "缺少冲突检测逻辑",
            "impact": "高",
            "fix_priority": 1
        },
        {
            "id": "RC-004",
            "category": "图层命名冲突",
            "problem": "图层名称可能重复",
            "description": "同名图片可能导致图层识别混乱",
            "code_location": "utils/photoshop_helper.py:696",
            "current_code": "图层命名逻辑不够唯一",
            "impact": "中",
            "fix_priority": 3
        }
    ]
    
    print(f"📋 发现 {len(root_causes)} 个根本原因:")
    
    for cause in root_causes:
        print(f"\n  {cause['id']}: {cause['category']} (优先级 {cause['fix_priority']})")
        print(f"    问题: {cause['problem']}")
        print(f"    描述: {cause['description']}")
        print(f"    位置: {cause['code_location']}")
        print(f"    影响: {cause['impact']}")
    
    return root_causes

def create_layer_order_fix():
    """创建图层顺序修复方案"""
    print("\n🔧 创建图层顺序修复方案")
    print("=" * 80)
    
    # 新的图层顺序管理策略
    layer_strategy = """
    图层顺序管理新策略：
    
    1. 保持自然顺序：不移动图层到底层
    2. 使用图层索引：按照放置顺序编号
    3. 正确的图层命名：包含唯一ID和顺序
    4. 图层属性设置：确保正确显示
    
    修复前问题：
    - 每个图层都移动到底层 → 顺序颠倒
    - 后放置的图片被覆盖 → 视觉错乱
    
    修复后效果：
    - 保持放置顺序 → 先放置的在底层
    - 后放置的在上层 → 符合预期
    """
    
    print(layer_strategy)
    
    # 生成修复的JavaScript代码
    fixed_js_code = '''
// 修复后的图层顺序管理JavaScript代码
function manageLayerOrderFixed(layerIndex, totalImages, customLayerName, uniqueId) {
    try {
        var doc = app.activeDocument;
        var currentLayer = doc.activeLayer;
        
        // 1. 设置唯一图层名称
        if (customLayerName && customLayerName !== "None" && customLayerName !== "") {
            currentLayer.name = customLayerName;
        } else {
            currentLayer.name = "RectPack_" + (layerIndex + 1) + "_of_" + totalImages + "_" + uniqueId;
        }
        
        // 2. 关键修复：不移动图层顺序，保持自然的堆叠顺序
        // 删除原来的错误代码：layer.move(layers[layers.length - 1], ElementPlacement.PLACEAFTER);
        // Photoshop的自然顺序：后创建的图层在上层，这正是我们想要的
        
        // 3. 设置图层属性确保正确显示
        currentLayer.blendMode = BlendMode.NORMAL;
        currentLayer.opacity = 100;
        currentLayer.visible = true;
        
        // 4. 解除所有锁定
        if (currentLayer.allLocked) currentLayer.allLocked = false;
        if (currentLayer.positionLocked) currentLayer.positionLocked = false;
        if (currentLayer.transparentPixelsLocked) currentLayer.transparentPixelsLocked = false;
        if (currentLayer.imagePixelsLocked) currentLayer.imagePixelsLocked = false;
        
        return "图层顺序管理成功: " + currentLayer.name + " (索引: " + layerIndex + ")";
        
    } catch(e) {
        return "图层顺序管理失败: " + e.toString();
    }
}
'''
    
    print("📋 修复后的JavaScript代码:")
    print(fixed_js_code)
    
    return fixed_js_code

def create_coordinate_validation_fix():
    """创建坐标验证修复方案"""
    print("\n🔧 创建坐标验证修复方案")
    print("=" * 80)
    
    validation_strategy = """
    坐标验证新策略：
    
    1. 放置前验证：检查新位置是否与已有图片冲突
    2. 边界检查：确保图片不超出画布边界
    3. 重叠检测：计算重叠面积，避免覆盖
    4. 位置调整：如果冲突，寻找替代位置
    
    验证流程：
    1. 算法计算位置 → 2. 冲突检测 → 3. 位置验证 → 4. PS放置
    """
    
    print(validation_strategy)
    
    # 生成坐标验证代码
    validation_code = '''
def validate_image_placement(self, x: int, y: int, width: int, height: int) -> Tuple[bool, str]:
    """
    验证图片放置位置是否有效
    
    Args:
        x: x坐标
        y: y坐标  
        width: 宽度
        height: 高度
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    # 1. 边界检查
    if x < 0 or y < 0:
        return False, f"坐标不能为负数: ({x}, {y})"
    
    if x + width > self.bin_width:
        return False, f"图片超出画布右边界: {x + width} > {self.bin_width}"
    
    if self.bin_height > 0 and y + height > self.bin_height:
        return False, f"图片超出画布下边界: {y + height} > {self.bin_height}"
    
    # 2. 重叠检测
    for placed_img in self.placed_images:
        if self._check_overlap(x, y, width, height, placed_img):
            return False, f"与已放置图片重叠: {placed_img.get('name', 'Unknown')}"
    
    return True, "位置有效"

def _check_overlap(self, x1: int, y1: int, w1: int, h1: int, placed_img: Dict) -> bool:
    """检查两个矩形是否重叠"""
    x2, y2, w2, h2 = placed_img['x'], placed_img['y'], placed_img['width'], placed_img['height']
    
    # 矩形重叠检测
    return not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1)
'''
    
    print("📋 坐标验证代码:")
    print(validation_code)
    
    return validation_code

def create_comprehensive_fix_plan():
    """创建综合修复计划"""
    print("\n📋 综合修复计划")
    print("=" * 80)
    
    fix_plan = {
        "phase1": {
            "name": "紧急修复阶段",
            "duration": "立即执行",
            "tasks": [
                {
                    "id": "FIX-001",
                    "name": "修复图层顺序管理",
                    "description": "删除错误的图层移动代码，保持自然堆叠顺序",
                    "files": ["utils/photoshop_helper.py"],
                    "priority": "最高"
                },
                {
                    "id": "FIX-002", 
                    "name": "修复图层索引计算",
                    "description": "使用正确的图层索引计算方法",
                    "files": ["core/rectpack_arranger.py"],
                    "priority": "高"
                }
            ]
        },
        "phase2": {
            "name": "验证增强阶段",
            "duration": "1-2小时",
            "tasks": [
                {
                    "id": "FIX-003",
                    "name": "添加坐标验证",
                    "description": "实现完整的坐标冲突检测",
                    "files": ["core/rectpack_arranger.py"],
                    "priority": "高"
                },
                {
                    "id": "FIX-004",
                    "name": "增强图层命名",
                    "description": "使用唯一ID确保图层名称不重复",
                    "files": ["utils/photoshop_helper.py"],
                    "priority": "中"
                }
            ]
        },
        "phase3": {
            "name": "测试验证阶段",
            "duration": "30分钟",
            "tasks": [
                {
                    "id": "TEST-001",
                    "name": "创建测试用例",
                    "description": "测试图片覆盖修复效果",
                    "files": ["test_overlap_fix.py"],
                    "priority": "中"
                },
                {
                    "id": "TEST-002",
                    "name": "实际环境验证",
                    "description": "在真实数据上验证修复效果",
                    "files": [],
                    "priority": "高"
                }
            ]
        }
    }
    
    print("📋 修复计划详情:")
    for phase_key, phase in fix_plan.items():
        print(f"\n  {phase['name']} ({phase['duration']}):")
        for task in phase['tasks']:
            print(f"    {task['id']}: {task['name']} (优先级: {task['priority']})")
            print(f"      描述: {task['description']}")
            if task['files']:
                print(f"      文件: {', '.join(task['files'])}")
    
    return fix_plan

def generate_fix_summary():
    """生成修复总结"""
    print("\n📊 图片覆盖问题修复总结")
    print("=" * 80)
    
    # 执行分析
    root_causes = analyze_overlap_root_causes()
    layer_fix = create_layer_order_fix()
    coord_fix = create_coordinate_validation_fix()
    fix_plan = create_comprehensive_fix_plan()
    
    print(f"\n🎯 修复总结:")
    print(f"  发现根本原因: {len(root_causes)} 个")
    print(f"  修复方案: 3 个主要方案")
    print(f"  修复阶段: 3 个阶段")
    
    print(f"\n🔧 关键修复点:")
    print(f"  1. ✅ 图层顺序：删除错误的图层移动代码")
    print(f"  2. ✅ 索引计算：修正图层索引传递逻辑")
    print(f"  3. ✅ 坐标验证：添加完整的冲突检测")
    print(f"  4. ✅ 图层命名：使用唯一ID避免冲突")
    
    print(f"\n🚀 预期效果:")
    print(f"  • 消除图片覆盖问题")
    print(f"  • 消除空白区域")
    print(f"  • 图片按算法预期位置排列")
    print(f"  • 提高布局准确性")
    
    return {
        'root_causes': root_causes,
        'layer_fix': layer_fix,
        'coordinate_fix': coord_fix,
        'fix_plan': fix_plan
    }

def main():
    """主函数"""
    print("🔧 图片覆盖问题综合修复分析")
    print("=" * 100)
    print("问题描述:")
    print("• 第一个红框：2个图片覆盖在其他图片之上")
    print("• 第二个红框：出现大片空白区域")
    print("• 根本原因：图层顺序管理和坐标验证问题")
    print("=" * 100)
    
    # 执行综合分析
    results = generate_fix_summary()
    
    print(f"\n✅ 综合修复分析完成！")
    print(f"📁 修复方案已准备好，可以开始实施修复")
    
    return results

if __name__ == "__main__":
    results = main()
    sys.exit(0)

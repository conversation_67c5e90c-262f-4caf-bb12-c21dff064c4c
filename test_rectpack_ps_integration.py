#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack算法PS集成测试脚本

专门测试RectPack算法与Photoshop的集成问题，
验证坐标和尺寸是否正确传递给PS

作者: RectPack算法测试团队
日期: 2024-12-19
版本: PS集成测试版
"""

import sys
import os
import time
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rectpack_coordinates_transmission():
    """
    测试RectPack算法坐标传递
    """
    print("🔍 测试RectPack算法坐标传递")
    print("=" * 80)
    
    try:
        # 模拟RectPack算法的输出
        arranged_images = [
            {
                'x': 0,
                'y': 0,
                'width': 100,
                'height': 80,
                'path': '/fake/path/test1.jpg',
                'name': 'test1',
                'need_rotation': False,
                'rotated': False
            },
            {
                'x': 105,  # 有5px间距
                'y': 0,
                'width': 80,
                'height': 60,
                'path': '/fake/path/test2.jpg',
                'name': 'test2',
                'need_rotation': False,
                'rotated': False
            },
            {
                'x': 0,
                'y': 85,  # 有5px间距
                'width': 120,
                'height': 70,
                'path': '/fake/path/test3.jpg',
                'name': 'test3',
                'need_rotation': False,
                'rotated': False
            }
        ]
        
        print(f"📋 模拟RectPack算法输出:")
        for i, img in enumerate(arranged_images, 1):
            print(f"  {i}. {img['name']}: 位置({img['x']},{img['y']}) 尺寸({img['width']}x{img['height']})")
        
        # 测试image_processor.place_image的数据传递
        print(f"\n🔧 测试image_processor.place_image数据传递:")
        
        from utils.image_processor import PhotoshopImageProcessor
        
        # 创建image_processor实例
        processor = PhotoshopImageProcessor()
        
        # 模拟place_image调用
        for i, img in enumerate(arranged_images, 1):
            place_info = {
                'image_path': img['path'],
                'x': img['x'],
                'y': img['y'],
                'width': img['width'],
                'height': img['height'],
                'rotated': img['rotated'],
                'name': img['name'],
                'image_class': 'C'
            }
            
            print(f"\n  测试图片 {i}: {img['name']}")
            print(f"    传入place_info: {place_info}")
            
            # 检查数据提取逻辑
            image_path = place_info.get('image_path', '')
            x = place_info.get('x', 0)
            y = place_info.get('y', 0)
            width = place_info.get('width', 0)
            height = place_info.get('height', 0)
            rotated = place_info.get('rotated', False)
            image_name = place_info.get('name', os.path.basename(image_path))
            
            print(f"    提取的数据:")
            print(f"      image_path: {image_path}")
            print(f"      x: {x}, y: {y}")
            print(f"      width: {width}, height: {height}")
            print(f"      rotated: {rotated}")
            print(f"      image_name: {image_name}")
            
            # 检查是否有数据丢失或转换
            if x != img['x'] or y != img['y']:
                print(f"      ❌ 坐标传递错误: 期望({img['x']},{img['y']}) 实际({x},{y})")
            else:
                print(f"      ✅ 坐标传递正确")
                
            if width != img['width'] or height != img['height']:
                print(f"      ❌ 尺寸传递错误: 期望({img['width']}x{img['height']}) 实际({width}x{height})")
            else:
                print(f"      ✅ 尺寸传递正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 坐标传递测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_photoshop_helper_parameters():
    """
    测试PhotoshopHelper.place_image参数处理
    """
    print("\n🔧 测试PhotoshopHelper.place_image参数处理")
    print("=" * 80)
    
    try:
        from utils.photoshop_helper import PhotoshopHelper
        import inspect
        
        # 检查place_image方法签名
        method_signature = inspect.signature(PhotoshopHelper.place_image)
        print(f"📋 PhotoshopHelper.place_image方法签名:")
        print(f"  {method_signature}")
        
        # 检查参数处理逻辑
        method_source = inspect.getsource(PhotoshopHelper.place_image)
        
        print(f"\n🔍 参数处理逻辑检查:")
        
        # 检查是否有单位转换
        if "x_cm" in method_source and "y_cm" in method_source:
            print(f"  ✅ 支持厘米单位输入")
        else:
            print(f"  ❌ 不支持厘米单位输入")
        
        # 检查像素参数处理
        if "x is None or y is None" in method_source:
            print(f"  ✅ 检查像素坐标参数")
        else:
            print(f"  ❌ 未检查像素坐标参数")
        
        # 检查图片尺寸调整
        if "resizeImage" in method_source:
            print(f"  ⚠️ 警告: 包含图片尺寸调整逻辑")
            print(f"    这可能会覆盖RectPack算法计算的精确尺寸")
        else:
            print(f"  ✅ 不包含图片尺寸调整逻辑")
        
        # 检查坐标定位逻辑
        if "translate" in method_source:
            print(f"  ✅ 使用translate方法定位图片")
        else:
            print(f"  ❌ 未使用translate方法定位图片")
        
        # 检查JavaScript代码中的坐标计算
        if "moveX = {x} - bounds[0].value" in method_source:
            print(f"  ✅ JavaScript代码正确计算移动距离")
        else:
            print(f"  ❌ JavaScript代码未正确计算移动距离")
        
        return True
        
    except Exception as e:
        print(f"❌ PhotoshopHelper参数处理测试失败: {str(e)}")
        return False

def test_rectpack_layout_worker_integration():
    """
    测试RectPackLayoutWorker的集成逻辑
    """
    print("\n🔧 测试RectPackLayoutWorker集成逻辑")
    print("=" * 80)
    
    try:
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        import inspect
        
        # 检查_create_photoshop_canvas方法
        method_source = inspect.getsource(RectPackLayoutWorker._create_photoshop_canvas)
        
        print(f"📋 检查_create_photoshop_canvas方法:")
        
        # 检查arranged_images的使用
        if "for i, image_info in enumerate(arranged_images)" in method_source:
            print(f"  ✅ 正确遍历arranged_images")
        else:
            print(f"  ❌ 未正确遍历arranged_images")
        
        # 检查坐标提取
        if "image_info.get('x'" in method_source:
            print(f"  ✅ 正确提取x坐标")
        else:
            print(f"  ❌ 未正确提取x坐标")
        
        if "image_info.get('y'" in method_source:
            print(f"  ✅ 正确提取y坐标")
        else:
            print(f"  ❌ 未正确提取y坐标")
        
        # 检查尺寸提取
        if "image_info.get('width'" in method_source:
            print(f"  ✅ 正确提取width")
        else:
            print(f"  ❌ 未正确提取width")
        
        if "image_info.get('height'" in method_source:
            print(f"  ✅ 正确提取height")
        else:
            print(f"  ❌ 未正确提取height")
        
        # 检查place_info构建
        if "place_info = {" in method_source:
            print(f"  ✅ 正确构建place_info")
        else:
            print(f"  ❌ 未正确构建place_info")
        
        # 检查image_processor调用
        if "self.image_processor.place_image(place_info)" in method_source:
            print(f"  ✅ 正确调用image_processor.place_image")
        else:
            print(f"  ❌ 未正确调用image_processor.place_image")
        
        return True
        
    except Exception as e:
        print(f"❌ RectPackLayoutWorker集成测试失败: {str(e)}")
        return False

def analyze_potential_issues():
    """
    分析潜在问题
    """
    print("\n🔍 分析潜在问题")
    print("=" * 80)
    
    issues = []
    
    try:
        from utils.photoshop_helper import PhotoshopHelper
        import inspect
        
        # 检查PhotoshopHelper.place_image方法
        method_source = inspect.getsource(PhotoshopHelper.place_image)
        
        # 问题1: 图片尺寸调整可能覆盖RectPack算法的精确尺寸
        if "resizeImage(width, height" in method_source:
            issues.append({
                'type': '尺寸覆盖问题',
                'description': 'PhotoshopHelper.place_image中的resizeImage调用可能覆盖RectPack算法计算的精确尺寸',
                'severity': '高',
                'location': 'utils/photoshop_helper.py:place_image方法',
                'solution': '确保resizeImage使用RectPack算法计算的精确尺寸'
            })
        
        # 问题2: 坐标计算逻辑
        if "moveX = {x} - bounds[0].value" in method_source:
            issues.append({
                'type': '坐标计算',
                'description': 'JavaScript代码中的坐标计算依赖于图层的当前bounds',
                'severity': '中',
                'location': 'utils/photoshop_helper.py:JavaScript代码',
                'solution': '验证bounds计算是否正确'
            })
        
        # 问题3: 旋转逻辑
        if "doc.rotateCanvas" in method_source:
            issues.append({
                'type': '旋转逻辑',
                'description': '旋转后的坐标和尺寸可能需要重新计算',
                'severity': '中',
                'location': 'utils/photoshop_helper.py:旋转处理',
                'solution': '确保旋转后的坐标和尺寸正确'
            })
        
        print(f"📊 发现 {len(issues)} 个潜在问题:")
        
        for i, issue in enumerate(issues, 1):
            print(f"\n  问题 {i}: {issue['type']}")
            print(f"    描述: {issue['description']}")
            print(f"    严重程度: {issue['severity']}")
            print(f"    位置: {issue['location']}")
            print(f"    建议解决方案: {issue['solution']}")
        
        if not issues:
            print(f"  ✅ 未发现明显的潜在问题")
        
        return issues
        
    except Exception as e:
        print(f"❌ 问题分析失败: {str(e)}")
        return []

def main():
    """
    主测试函数
    """
    print("🔬 RectPack算法PS集成深度测试")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证RectPack算法坐标是否正确传递给PS")
    print("2. 🔍 检查PhotoshopHelper.place_image参数处理")
    print("3. 🔍 测试RectPackLayoutWorker集成逻辑")
    print("4. 🔍 分析潜在的坐标覆盖或转换问题")
    print("=" * 100)
    
    # 执行测试
    results = []
    
    # 测试1: 坐标传递
    result1 = test_rectpack_coordinates_transmission()
    results.append(("RectPack坐标传递", result1))
    
    # 测试2: PhotoshopHelper参数处理
    result2 = test_photoshop_helper_parameters()
    results.append(("PhotoshopHelper参数处理", result2))
    
    # 测试3: RectPackLayoutWorker集成
    result3 = test_rectpack_layout_worker_integration()
    results.append(("RectPackLayoutWorker集成", result3))
    
    # 分析4: 潜在问题
    issues = analyze_potential_issues()
    results.append(("潜在问题分析", len(issues) == 0))
    
    # 输出测试结果
    print("\n" + "=" * 100)
    print("📊 PS集成测试结果汇总:")
    print("=" * 100)
    
    passed_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n📈 总体结果: {passed_count}/{len(results)} 项测试通过")
    
    if passed_count == len(results):
        print("🎉 所有测试通过！PS集成正常！")
    else:
        print("⚠️ 发现问题，需要进一步修复。")
    
    # 输出修复建议
    if issues:
        print("\n💡 修复建议:")
        for i, issue in enumerate(issues, 1):
            if issue['severity'] == '高':
                print(f"  🔥 优先修复: {issue['type']} - {issue['solution']}")
            elif issue['severity'] == '中':
                print(f"  ⚠️ 建议修复: {issue['type']} - {issue['solution']}")
            else:
                print(f"  💡 可选修复: {issue['type']} - {issue['solution']}")
    
    print("\n🔧 下一步行动:")
    print("1. 如果发现尺寸覆盖问题，需要修复PhotoshopHelper.place_image方法")
    print("2. 如果发现坐标计算问题，需要验证JavaScript代码的bounds计算")
    print("3. 如果发现旋转问题，需要确保旋转后的坐标和尺寸正确")
    print("4. 建议在实际PS环境中测试验证修复效果")
    
    return passed_count == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

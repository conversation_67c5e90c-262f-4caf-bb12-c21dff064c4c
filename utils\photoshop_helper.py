#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Photoshop辅助模块

提供Photoshop操作的辅助功能：
1. 检查Photoshop安装和运行状态
2. 启动Photoshop应用
3. 处理图像和画布操作
"""

import os
import sys
import logging
import subprocess
import time
from typing import Dict, Any
import photoshop.api as ps

# 导入常量模块
from utils.constants import get_constant

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("PhotoshopHelper")

class PhotoshopHelper:
    """Photoshop 辅助工具类，用于检查和启动 Photoshop"""

    # 缓存的Photoshop版本信息
    _ps_version_info = None

    # 兼容性模式设置
    _compatibility_mode = None

    # 日志信号
    _log_signal = None

    @staticmethod
    def set_log_signal(log_signal):
        """设置日志信号，用于向UI发送日志信息

        Args:
            log_signal: 日志信号对象
        """
        PhotoshopHelper._log_signal = log_signal

    @staticmethod
    def log(message):
        """发送日志信息

        Args:
            message: 日志信息
        """
        # 记录到标准日志
        log.info(message)

        # 如果设置了日志信号，也发送到UI
        if PhotoshopHelper._log_signal:
            try:
                PhotoshopHelper._log_signal.emit(message)
            except Exception as e:
                log.error(f"发送日志到UI失败: {str(e)}")

    @staticmethod
    def check_photoshop():
        """
        检查 Photoshop 是否可用

        Returns:
            tuple: (是否可用, 状态消息)
        """
        try:
            # 尝试连接 Photoshop
            try:
                app = ps.Application()
                version = app.Version

                # 获取并缓存版本信息
                PhotoshopHelper._ps_version_info = PhotoshopHelper._parse_version(version)

                # 设置兼容性模式
                PhotoshopHelper._set_compatibility_mode(PhotoshopHelper._ps_version_info)

                return True, f"Photoshop: 已连接 (v{version}, 兼容性模式: {PhotoshopHelper._compatibility_mode})"
            except Exception as e:
                if "Photoshop is not installed" in str(e):
                    return False, "Photoshop: 未安装"
                elif "Photoshop is not running" in str(e):
                    return False, "Photoshop: 未运行"
                else:
                    return False, f"Photoshop: 连接错误 ({str(e)})"

        except Exception as e:
            log.error(f"检查 Photoshop 时出错: {str(e)}")
            return False, f"Photoshop: 检查失败 ({str(e)})"

    @staticmethod
    def start_photoshop():
        """
        尝试启动 Photoshop

        Returns:
            tuple: (是否成功, 状态消息)
        """
        try:
            # 检查是否已经运行
            is_running, _ = PhotoshopHelper.check_photoshop()
            if is_running:
                return True, "Photoshop 已经在运行"

            # 尝试找到 Photoshop 安装路径
            ps_path = PhotoshopHelper._get_photoshop_path()
            if not ps_path:
                return False, "找不到 Photoshop 安装路径"

            # 尝试启动 Photoshop
            log.info(f"尝试启动 Photoshop: {ps_path}")
            if sys.platform == "win32":
                subprocess.Popen([ps_path])
            elif sys.platform == "darwin":  # macOS
                subprocess.Popen(["open", ps_path])
            else:
                return False, f"不支持的平台: {sys.platform}"

            # 等待启动
            log.info("等待 Photoshop 启动...")
            for _ in range(10):  # 最多等待10次
                time.sleep(2)  # 每次等待2秒
                is_running, _ = PhotoshopHelper.check_photoshop()
                if is_running:
                    return True, "Photoshop 已成功启动"

            return False, "Photoshop 启动超时"

        except Exception as e:
            log.error(f"启动 Photoshop 时出错: {str(e)}")
            return False, f"启动失败: {str(e)}"

    @staticmethod
    def _get_photoshop_path():
        """
        获取 Photoshop 安装路径

        Returns:
            str: Photoshop 可执行文件路径，未找到返回 None
        """
        try:
            if sys.platform == "win32":
                # Windows 平台
                possible_paths = [
                    # 最新版本
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop 2024", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop 2023", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop 2022", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop 2021", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop 2020", "Photoshop.exe"),
                    # CC 版本
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CC 2019", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CC 2018", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CC 2017", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CC 2015.5", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CC 2015", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CC 2014", "Photoshop.exe"),
                    # CS 版本
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CS6", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CS5", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CS4", "Photoshop.exe"),
                    os.path.join(os.environ["ProgramFiles"], "Adobe", "Adobe Photoshop CS3", "Photoshop.exe"),
                ]

                # 检查 x86 路径 (32位)
                if "ProgramFiles(x86)" in os.environ:
                    x86_paths = [
                        os.path.join(os.environ["ProgramFiles(x86)"], "Adobe", "Adobe Photoshop CS6", "Photoshop.exe"),
                        os.path.join(os.environ["ProgramFiles(x86)"], "Adobe", "Adobe Photoshop CS5", "Photoshop.exe"),
                        os.path.join(os.environ["ProgramFiles(x86)"], "Adobe", "Adobe Photoshop CS4", "Photoshop.exe"),
                        os.path.join(os.environ["ProgramFiles(x86)"], "Adobe", "Adobe Photoshop CS3", "Photoshop.exe"),
                    ]
                    possible_paths.extend(x86_paths)

                # 查找第一个存在的路径
                for path in possible_paths:
                    if os.path.exists(path):
                        return path

            elif sys.platform == "darwin":  # macOS
                possible_paths = [
                    "/Applications/Adobe Photoshop 2024/Adobe Photoshop 2024.app",
                    "/Applications/Adobe Photoshop 2023/Adobe Photoshop 2023.app",
                    "/Applications/Adobe Photoshop 2022/Adobe Photoshop 2022.app",
                    "/Applications/Adobe Photoshop 2021/Adobe Photoshop 2021.app",
                    "/Applications/Adobe Photoshop 2020/Adobe Photoshop 2020.app",
                    "/Applications/Adobe Photoshop CC 2019/Adobe Photoshop CC 2019.app",
                    "/Applications/Adobe Photoshop CC 2018/Adobe Photoshop CC 2018.app",
                    "/Applications/Adobe Photoshop CC 2017/Adobe Photoshop CC 2017.app",
                    "/Applications/Adobe Photoshop CC 2015.5/Adobe Photoshop CC 2015.5.app",
                    "/Applications/Adobe Photoshop CC 2015/Adobe Photoshop CC 2015.app",
                    "/Applications/Adobe Photoshop CC 2014/Adobe Photoshop CC 2014.app",
                    "/Applications/Adobe Photoshop CS6/Adobe Photoshop CS6.app",
                    "/Applications/Adobe Photoshop CS5/Adobe Photoshop CS5.app",
                    "/Applications/Adobe Photoshop CS4/Adobe Photoshop CS4.app",
                    "/Applications/Adobe Photoshop CS3/Adobe Photoshop CS3.app",
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        return path

            # 未找到 Photoshop
            return None

        except Exception as e:
            log.error(f"获取 Photoshop 路径时出错: {str(e)}")
            return None

    @staticmethod
    def purge_memory() -> bool:
        """
        清理Photoshop内存

        使用更健壮的方法清理Photoshop内存，避免"没有这种元素"错误

        Returns:
            是否成功
        """
        try:
            app = ps.Application()

            # 使用JavaScript执行内存清理
            js_script = """
            (function() {
                // 设置无对话框模式
                app.displayDialogs = DialogModes.NO;

                try {
                    // 尝试不同的清理方法
                    var success = false;

                    // 方法1: 使用purge方法清理所有缓存
                    try {
                        app.purge(PurgeTarget.ALLCACHES);
                        success = true;
                    } catch(e) {
                        // 忽略错误，尝试下一个方法
                    }

                    // 方法2: 单独清理各种缓存
                    try { app.purge(PurgeTarget.HISTORYCACHES); } catch(e) {}
                    try { app.purge(PurgeTarget.CLIPBOARDCACHE); } catch(e) {}
                    try { app.purge(PurgeTarget.UNDOCACHES); } catch(e) {}

                    // 方法3: 使用executeAction清理
                    try {
                        var idpurge = charIDToTypeID("Prge");
                        var desc = new ActionDescriptor();
                        var idPrgT = charIDToTypeID("PrgT");
                        var idPrgA = charIDToTypeID("PrgA");
                        desc.putEnumerated(idPrgT, idPrgT, idPrgA);
                        executeAction(idpurge, desc, DialogModes.NO);
                        success = true;
                    } catch(e) {
                        // 忽略错误
                    }

                    return success ? "成功清理内存" : "清理内存可能不完全";
                } catch(e) {
                    return "清理内存失败: " + e.toString();
                }
            })();
            """

            result = app.doJavaScript(js_script)
            log.info(f"内存清理结果: {result}")
            return "成功" in result or "可能不完全" in result
        except Exception as e:
            log.error(f"清理内存失败: {str(e)}")
            return False

    @staticmethod
    def create_canvas(width: int = None, height: int = None, name: str = "", ppi: int = 72,
                     width_cm: float = None, height_cm: float = None) -> bool:
        """创建新画布

        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 文档名称
            ppi: 分辨率（每英寸像素数）
            width_cm: 画布宽度（厘米）
            height_cm: 画布高度（厘米）

        Returns:
            是否成功
        """
        # 检查Photoshop是否可用
        is_available, message = PhotoshopHelper.check_photoshop()
        if not is_available:
            PhotoshopHelper.log(f"无法创建画布: {message}")
            return False

        try:
            # 处理单位转换：如果提供了cm单位，转换为像素
            if width_cm is not None and height_cm is not None:
                # 1厘米 = 0.393701英寸，像素 = 英寸 * PPI
                width = int(width_cm * 0.393701 * ppi)
                height = int(height_cm * 0.393701 * ppi)
                PhotoshopHelper.log(f"厘米转像素: {width_cm}x{height_cm}cm -> {width}x{height}px (PPI={ppi})")
            elif width is None or height is None:
                PhotoshopHelper.log("错误: 必须提供像素尺寸或厘米尺寸")
                return False
            # 检查画布尺寸是否超过最大限制
            max_width_px = get_constant('PS_MAX_CANVAS_WIDTH_PX', 35433)  # 默认3米
            max_height_px = get_constant('PS_MAX_CANVAS_HEIGHT_PX', 590551)  # 默认50米

            if width > max_width_px:
                PhotoshopHelper.log(f"警告: 画布宽度 {width}px 超过最大限制 {max_width_px}px，将被截断")
                width = max_width_px

            if height > max_height_px:
                PhotoshopHelper.log(f"警告: 画布高度 {height}px 超过最大限制 {max_height_px}px，将被截断")
                height = max_height_px

            PhotoshopHelper.log(f"创建画布: {name} ({width}x{height}像素, {ppi}PPI)")
            app = ps.Application()

            # 使用常量代替硬编码值
            color_mode = get_constant('PS_COLOR_MODE_RGB', 1)  # RGB 模式
            background = get_constant('PS_BACKGROUND_WHITE', 1)  # 白色背景

            # 创建文档
            app.documents.add(
                width, height,
                ppi, name,
                color_mode,  # 明确使用RGB模式
                background   # 白色背景
            )

            # 初始化画布设置，确保使用RGB模式
            js_init_canvas = f"""
            try {{
                // 获取当前文档
                var doc = app.activeDocument;

                // 设置标尺单位为像素
                app.preferences.rulerUnits = Units.PIXELS;

                // 设置原点为左上角
                app.preferences.pointType = PointType.POSTSCRIPT;

                // 设置显示选项
                app.displayDialogs = DialogModes.NO;

                // 确保使用RGB颜色模式
                if (doc.mode != DocumentMode.RGB) {{
                    doc.changeMode(DocumentMode.RGB);
                }}

                // 确保画布尺寸正确
                if (doc.width.value != {width} || doc.height.value != {height}) {{
                    doc.resizeCanvas({width}, {height});
                }}

                "画布初始化成功，使用RGB颜色模式";
            }} catch(e) {{
                "画布初始化失败: " + e.toString();
            }}
            """

            init_result = app.doJavaScript(js_init_canvas)
            PhotoshopHelper.log(f"画布初始化结果: {init_result}")

            PhotoshopHelper.log(f"画布 {name} 创建成功")
            return True
        except Exception as e:
            error_message = f"创建画布失败: {str(e)}"
            log.error(error_message)
            PhotoshopHelper.log(error_message)
            return False

    @staticmethod
    def validate_and_fix_coordinates(x, y, width, height, canvas_width=None, canvas_height=None):
        """
        验证和修复坐标精度问题

        Args:
            x: x坐标
            y: y坐标
            width: 宽度
            height: 高度
            canvas_width: 画布宽度（可选）
            canvas_height: 画布高度（可选）

        Returns:
            tuple: (validated_x, validated_y, validated_width, validated_height, is_valid)
        """
        try:
            # 步骤1: 强制转换为整数，确保精度
            # 使用round()函数进行四舍五入
            validated_x = int(round(float(x))) if x is not None else 0
            validated_y = int(round(float(y))) if y is not None else 0
            validated_width = int(round(float(width))) if width is not None else 0
            validated_height = int(round(float(height))) if height is not None else 0

            # 步骤2: 基本有效性检查
            if validated_width <= 0 or validated_height <= 0:
                PhotoshopHelper.log(f"❌ 无效的图片尺寸: {validated_width}x{validated_height}")
                return validated_x, validated_y, validated_width, validated_height, False

            if validated_x < 0 or validated_y < 0:
                PhotoshopHelper.log(f"❌ 无效的坐标位置: ({validated_x},{validated_y})")
                return validated_x, validated_y, validated_width, validated_height, False

            # 步骤3: 画布边界检查（如果提供了画布尺寸）
            if canvas_width is not None and validated_x + validated_width > canvas_width:
                PhotoshopHelper.log(f"❌ 图片超出画布右边界: {validated_x + validated_width} > {canvas_width}")
                return validated_x, validated_y, validated_width, validated_height, False

            if canvas_height is not None and validated_y + validated_height > canvas_height:
                PhotoshopHelper.log(f"❌ 图片超出画布下边界: {validated_y + validated_height} > {canvas_height}")
                return validated_x, validated_y, validated_width, validated_height, False

            PhotoshopHelper.log(f"✅ 坐标验证通过: 位置({validated_x},{validated_y}) 尺寸({validated_width}x{validated_height})")
            return validated_x, validated_y, validated_width, validated_height, True

        except (ValueError, TypeError) as e:
            PhotoshopHelper.log(f"❌ 坐标转换失败: {str(e)}")
            return 0, 0, 0, 0, False

    @staticmethod
    def validate_layer_placement(expected_x: int, expected_y: int, expected_width: int, expected_height: int,
                               tolerance: int = 2) -> Dict[str, Any]:
        """
        验证图层放置的准确性

        Args:
            expected_x: 预期x坐标
            expected_y: 预期y坐标
            expected_width: 预期宽度
            expected_height: 预期高度
            tolerance: 容差范围（像素）

        Returns:
            Dict: 验证结果
        """
        try:
            # 使用JavaScript获取当前图层信息
            js_validation_script = f"""
            try {{
                var layer = app.activeDocument.activeLayer;
                var bounds = layer.bounds;

                // 获取实际边界
                var actualLeft = Math.round(bounds[0].value);
                var actualTop = Math.round(bounds[1].value);
                var actualRight = Math.round(bounds[2].value);
                var actualBottom = Math.round(bounds[3].value);

                // 计算实际尺寸
                var actualWidth = actualRight - actualLeft;
                var actualHeight = actualBottom - actualTop;

                // 验证位置
                var positionErrorX = Math.abs(actualLeft - {expected_x});
                var positionErrorY = Math.abs(actualTop - {expected_y});

                // 验证尺寸
                var sizeErrorWidth = Math.abs(actualWidth - {expected_width});
                var sizeErrorHeight = Math.abs(actualHeight - {expected_height});

                // 检查是否在容差范围内
                var positionValid = (positionErrorX <= {tolerance}) && (positionErrorY <= {tolerance});
                var sizeValid = (sizeErrorWidth <= {tolerance}) && (sizeErrorHeight <= {tolerance});

                var result = {{
                    "valid": positionValid && sizeValid,
                    "layer_name": layer.name,
                    "actual_position": [actualLeft, actualTop],
                    "actual_size": [actualWidth, actualHeight],
                    "expected_position": [{expected_x}, {expected_y}],
                    "expected_size": [{expected_width}, {expected_height}],
                    "position_error": [positionErrorX, positionErrorY],
                    "size_error": [sizeErrorWidth, sizeErrorHeight],
                    "position_valid": positionValid,
                    "size_valid": sizeValid
                }};

                JSON.stringify(result);

            }} catch(e) {{
                JSON.stringify({{"error": e.toString()}});
            }}
            """

            # 执行JavaScript并解析结果
            import json
            result_str = PhotoshopHelper.app.doJavaScript(js_validation_script)
            result = json.loads(result_str)

            if "error" in result:
                PhotoshopHelper.log(f"❌ 图层验证失败: {result['error']}")
                return {"valid": False, "error": result["error"]}

            # 记录验证结果
            if result["valid"]:
                PhotoshopHelper.log(f"✅ 图层验证通过: {result['layer_name']} 位置{result['actual_position']} 尺寸{result['actual_size']}")
            else:
                PhotoshopHelper.log(f"⚠️ 图层验证失败: {result['layer_name']}")
                PhotoshopHelper.log(f"  预期位置: {result['expected_position']}, 实际位置: {result['actual_position']}, 误差: {result['position_error']}")
                PhotoshopHelper.log(f"  预期尺寸: {result['expected_size']}, 实际尺寸: {result['actual_size']}, 误差: {result['size_error']}")

            return result

        except Exception as e:
            PhotoshopHelper.log(f"❌ 图层验证异常: {str(e)}")
            return {"valid": False, "error": str(e)}

    @staticmethod
    def place_image(image_path: str, x: int = None, y: int = None, width: int = None, height: int = None,
                   rotate: bool = False, rotation: int = 0, x_cm: float = None, y_cm: float = None,
                   width_cm: float = None, height_cm: float = None, ppi: int = 72,
                   layer_index: int = 0, total_images: int = 1) -> bool:
        """放置图片到画布

        Args:
            image_path: 图片路径
            x: 目标x坐标（像素）
            y: 目标y坐标（像素）
            width: 目标宽度（像素）
            height: 目标高度（像素）
            rotate: 是否旋转90度（兼容参数）
            rotation: 旋转角度（0, 90, 180, 270）
            x_cm: 目标x坐标（厘米）
            y_cm: 目标y坐标（厘米）
            width_cm: 目标宽度（厘米）
            height_cm: 目标高度（厘米）
            ppi: 分辨率（用于cm转换）

        Returns:
            是否成功
        """
        # 检查Photoshop是否可用
        is_available, message = PhotoshopHelper.check_photoshop()
        if not is_available:
            PhotoshopHelper.log(f"无法放置图片: {message}")
            return False

        try:
            # 步骤1: 处理单位转换：如果提供了cm单位，转换为像素
            if x_cm is not None and y_cm is not None and width_cm is not None and height_cm is not None:
                # 1厘米 = 0.393701英寸，像素 = 英寸 * PPI
                x = int(round(x_cm * 0.393701 * ppi))  # 使用round确保精度
                y = int(round(y_cm * 0.393701 * ppi))
                width = int(round(width_cm * 0.393701 * ppi))
                height = int(round(height_cm * 0.393701 * ppi))
                PhotoshopHelper.log(f"厘米转像素: 位置({x_cm},{y_cm})cm -> ({x},{y})px, 尺寸{width_cm}x{height_cm}cm -> {width}x{height}px (PPI={ppi})")
            elif x is None or y is None or width is None or height is None:
                PhotoshopHelper.log("错误: 必须提供像素坐标尺寸或厘米坐标尺寸")
                return False

            # 步骤2: 验证和修复坐标精度
            validated_x, validated_y, validated_width, validated_height, is_valid = PhotoshopHelper.validate_and_fix_coordinates(
                x, y, width, height
            )

            if not is_valid:
                PhotoshopHelper.log(f"❌ 坐标验证失败，无法放置图片: {image_name}")
                return False

            # 使用验证后的坐标
            x, y, width, height = validated_x, validated_y, validated_width, validated_height

            # 处理旋转参数：兼容旧的rotate参数和新的rotation参数
            actual_rotation = 0
            if rotate:
                actual_rotation = 90
            elif rotation != 0:
                actual_rotation = rotation
            # 获取图片文件名
            image_name = os.path.basename(image_path)

            # 添加详细日志用于调试RectPack算法布局问题
            PhotoshopHelper.log(f"📍 PhotoshopHelper接收参数:")
            PhotoshopHelper.log(f"  • 图片文件: {image_name}")
            PhotoshopHelper.log(f"  • 完整路径: {image_path}")
            PhotoshopHelper.log(f"  • 像素坐标: ({x}, {y})")
            PhotoshopHelper.log(f"  • 像素尺寸: {width}x{height}")
            PhotoshopHelper.log(f"  • 旋转角度: {actual_rotation}度")
            PhotoshopHelper.log(f"  • PPI设置: {ppi}")

            app = ps.Application()
            # 打开图片
            image_doc = app.open(image_path)

            # 获取图片原始尺寸 - 修复float类型错误
            try:
                # 尝试获取.value属性
                original_width = image_doc.width.value if hasattr(image_doc.width, 'value') else float(image_doc.width)
                original_height = image_doc.height.value if hasattr(image_doc.height, 'value') else float(image_doc.height)
            except Exception as e:
                # 如果获取失败，尝试直接转换为float
                PhotoshopHelper.log(f"获取图片尺寸时遇到问题: {str(e)}，尝试直接转换")
                original_width = float(image_doc.width)
                original_height = float(image_doc.height)

            PhotoshopHelper.log(f"图片原始尺寸: {original_width}x{original_height}px")

            # 确保尺寸为整数
            original_width = int(original_width)
            original_height = int(original_height)

            # 检查是否需要调整尺寸
            # 只有当目标尺寸与原始尺寸不同时才调整
            size_needs_adjustment = (abs(original_width - width) > 1 or abs(original_height - height) > 1)

            # 如果需要旋转，首先执行旋转操作，然后再调整大小
            if actual_rotation != 0:
                # 使用JavaScript执行旋转操作
                js_rotate_script = f"""
                try {{
                    // 获取当前文档
                    var doc = app.activeDocument;

                    // 旋转指定角度
                    doc.rotateCanvas({actual_rotation});

                    "图片旋转{actual_rotation}度成功";
                }} catch(e) {{
                    "旋转图片失败: " + e.toString();
                }}
                """
                PhotoshopHelper.log(f"旋转图片{actual_rotation}度...")
                rotate_result = app.doJavaScript(js_rotate_script)
                PhotoshopHelper.log(f"旋转图片结果: {rotate_result}")

                # 旋转后需要重新获取尺寸 - 修复float类型错误
                try:
                    rotated_width = image_doc.width.value if hasattr(image_doc.width, 'value') else float(image_doc.width)
                    rotated_height = image_doc.height.value if hasattr(image_doc.height, 'value') else float(image_doc.height)
                except Exception as e:
                    PhotoshopHelper.log(f"获取旋转后图片尺寸时遇到问题: {str(e)}，尝试直接转换")
                    rotated_width = float(image_doc.width)
                    rotated_height = float(image_doc.height)

                # 确保尺寸为整数
                rotated_width = int(rotated_width)
                rotated_height = int(rotated_height)
                PhotoshopHelper.log(f"旋转后尺寸: {rotated_width}x{rotated_height}px")

                # 检查旋转后是否需要调整尺寸
                if abs(rotated_width - width) > 1 or abs(rotated_height - height) > 1:
                    PhotoshopHelper.log(f"调整旋转后图片大小: {width}x{height}px")
                    image_doc.resizeImage(width, height, app.activeDocument.resolution)
                else:
                    PhotoshopHelper.log(f"旋转后尺寸已符合要求，无需调整")
            else:
                # 不旋转，检查是否需要调整尺寸
                if size_needs_adjustment:
                    PhotoshopHelper.log(f"调整图片大小: {original_width}x{original_height}px -> {width}x{height}px")
                    image_doc.resizeImage(width, height, app.activeDocument.resolution)
                else:
                    PhotoshopHelper.log(f"图片尺寸已符合要求，无需调整")

            # 复制到主文档
            image_doc.selection.selectAll()
            image_doc.selection.copy()
            # 使用整数值代替枚举常量，避免版本兼容性问题
            # 2 = 不保存更改
            image_doc.close(2)  # 相当于 SaveOptions.DONOTSAVECHANGES

            # 粘贴并精确定位图层 - 优化的JavaScript坐标计算 + 图层管理
            js_paste_position_script = f"""

    try {{
        // 获取当前文档和图层
        var doc = app.activeDocument;
        
        // 步骤1: 验证输入参数
        var targetX = {x};
        var targetY = {y};
        var targetWidth = {width};
        var targetHeight = {height};
        var layerIndex = {layer_index};
        var totalImages = {total_images};
        
        if (targetX < 0 || targetY < 0 || targetWidth <= 0 || targetHeight <= 0) {{
            "错误: 无效的坐标或尺寸参数 - X:" + targetX + " Y:" + targetY + " W:" + targetWidth + " H:" + targetHeight;
        }}
        
        // 步骤2: 粘贴图片
        app.activeDocument.paste();
        var layer = doc.activeLayer;
        
        // 步骤3: 立即设置图层名称和属性
        layer.name = "RectPack_Image_" + (layerIndex + 1) + "_of_" + totalImages;
        layer.blendMode = BlendMode.NORMAL;
        layer.opacity = 100;
        layer.visible = true;
        
        // 步骤4: 解除所有锁定
        if (layer.allLocked) layer.allLocked = false;
        if (layer.positionLocked) layer.positionLocked = false;
        if (layer.transparentPixelsLocked) layer.transparentPixelsLocked = false;
        if (layer.imagePixelsLocked) layer.imagePixelsLocked = false;
        
        // 步骤5: 使用绝对定位方法 - 直接设置图层位置
        // 这是关键修复：不使用translate，而是直接设置位置
        var deltaX = targetX;
        var deltaY = targetY;
        
        // 获取当前位置
        var currentBounds = layer.bounds;
        var currentX = Math.round(currentBounds[0].value);
        var currentY = Math.round(currentBounds[1].value);
        
        // 计算需要移动的距离
        var moveX = targetX - currentX;
        var moveY = targetY - currentY;
        
        // 执行移动 - 使用UnitValue确保精确定位
        layer.translate(UnitValue(moveX, "px"), UnitValue(moveY, "px"));
        
        // 步骤6: 验证位置是否正确
        var finalBounds = layer.bounds;
        var finalX = Math.round(finalBounds[0].value);
        var finalY = Math.round(finalBounds[1].value);
        var finalWidth = Math.round(finalBounds[2].value - finalBounds[0].value);
        var finalHeight = Math.round(finalBounds[3].value - finalBounds[1].value);
        
        // 步骤7: 如果位置不准确，尝试二次修正
        var positionError = Math.abs(finalX - targetX) + Math.abs(finalY - targetY);
        if (positionError > 2) {{
            // 二次修正
            var correctionX = targetX - finalX;
            var correctionY = targetY - finalY;
            layer.translate(UnitValue(correctionX, "px"), UnitValue(correctionY, "px"));
            
            // 重新验证
            var correctedBounds = layer.bounds;
            finalX = Math.round(correctedBounds[0].value);
            finalY = Math.round(correctedBounds[1].value);
            positionError = Math.abs(finalX - targetX) + Math.abs(finalY - targetY);
        }}
        
        // 步骤8: 图层顺序管理（在定位完成后）
        var layers = doc.layers;
        if (layers.length > 1) {{
            layer.move(layers[layers.length - 1], ElementPlacement.PLACEAFTER);
        }}
        
        // 返回详细结果
        var result = "紧急修复成功: 图层[" + layer.name + "] ";
        result += "目标位置(" + targetX + "," + targetY + ") ";
        result += "实际位置(" + finalX + "," + finalY + ") ";
        result += "移动距离(" + moveX + "," + moveY + ") ";
        result += "位置误差=" + positionError + "像素";
        
        if (positionError <= 2) {{
            result += " [定位成功]";
        }} else {{
            result += " [定位失败]";
        }}
        
        result;
        
    }} catch(e) {{
        "紧急修复失败: " + e.toString();
    }}
    
            """

            paste_result = app.doJavaScript(js_paste_position_script)
            PhotoshopHelper.log(f"粘贴和定位图层结果: {paste_result}")

            # 步骤11: 验证图层放置的准确性
            validation_result = PhotoshopHelper.validate_layer_placement(x, y, width, height, tolerance=3)

            if validation_result.get("valid", False):
                PhotoshopHelper.log(f"✅ 图片放置成功: {image_name} -> 位置({x},{y}) 尺寸({width}x{height}) [验证通过]")
            else:
                PhotoshopHelper.log(f"⚠️ 图片放置成功但位置可能不准确: {image_name} -> 位置({x},{y}) 尺寸({width}x{height}) [验证失败]")

            return True

        except Exception as e:
            error_message = f"放置图片失败: {str(e)}"
            log.error(error_message)
            PhotoshopHelper.log(error_message)
            return False

    @staticmethod
    def save_document(file_path: str, format: str = 'TIFF') -> bool:
        """保存文档

        Args:
            file_path: 保存路径
            format: 文件格式（'TIFF'或'JPEG'）

        Returns:
            是否成功
        """
        # 检查Photoshop是否可用
        is_available, message = PhotoshopHelper.check_photoshop()
        if not is_available:
            PhotoshopHelper.log(f"无法保存文档: {message}")
            return False

        try:
            # 获取文件名
            file_name = os.path.basename(file_path)
            PhotoshopHelper.log(f"保存文档: {file_name} 格式: {format}")

            app = ps.Application()

            # 方法1: 尝试使用高级JavaScript保存
            try:
                PhotoshopHelper.log("尝试使用高级JavaScript方法保存文件...")
                result = PhotoshopHelper._save_with_advanced_js(file_path, format)
                if result:
                    PhotoshopHelper.log(f"文档 {file_name} 保存成功 (高级JavaScript方法)")
                    return True
            except Exception as e1:
                error_message = f"高级JavaScript保存失败: {str(e1)}"
                log.error(error_message)
                PhotoshopHelper.log(error_message)

            # 方法2: 使用标准TiffSaveOptions
            try:
                PhotoshopHelper.log("尝试使用标准SaveOptions保存文件...")
                if format.upper() == 'TIFF':
                    options = ps.TiffSaveOptions()
                    options.imageCompression = 1  # LZW压缩
                    options.layerCompression = 1  # RLE压缩
                    options.byteOrder = 1  # IBM PC
                    options.embedColorProfile = True
                    options.alphaChannels = False
                    options.annotations = False
                    options.layers = False
                    options.transparency = False
                else:
                    options = ps.JPEGSaveOptions()
                    options.quality = 12
                    options.embedColorProfile = True

                app.activeDocument.saveAs(file_path, options, True)
                PhotoshopHelper.log(f"文档 {file_name} 保存成功 (标准SaveOptions方法)")
                return True
            except Exception as e2:
                error_message = f"使用标准SaveOptions保存失败: {str(e2)}"
                log.error(error_message)
                PhotoshopHelper.log(error_message)

            # 方法3: 尝试使用简单JavaScript保存
            try:
                PhotoshopHelper.log("尝试使用简单JavaScript保存文件...")
                js_save_script = f"""
                (function() {{
                    try {{
                        // 确保文档是RGB模式
                        if (app.activeDocument.mode != DocumentMode.RGB) {{
                            app.activeDocument.changeMode(DocumentMode.RGB);
                        }}

                        var f = new File("{file_path.replace('\\', '\\\\')}");
                        var saveFile = app.activeDocument.saveAs(f);
                        return "成功";
                    }} catch(e) {{
                        return "错误: " + e;
                    }}
                }})();
                """

                result = app.doJavaScript(js_save_script)
                if "成功" in result:
                    PhotoshopHelper.log(f"文档 {file_name} 保存成功 (简单JavaScript方法)")
                    return True
                else:
                    error_message = f"简单JavaScript保存失败: {result}"
                    log.error(error_message)
                    PhotoshopHelper.log(error_message)
            except Exception as e3:
                error_message = f"简单JavaScript保存失败: {str(e3)}"
                log.error(error_message)
                PhotoshopHelper.log(error_message)

            # 所有方法都失败
            error_message = "所有保存方法都失败"
            log.error(error_message)
            PhotoshopHelper.log(error_message)
            return False

        except Exception as e:
            error_message = f"保存文档失败: {str(e)}"
            log.error(error_message)
            PhotoshopHelper.log(error_message)
            return False

    @staticmethod
    def save_as_tiff(file_path: str) -> bool:
        """保存当前文档为TIFF格式

        Args:
            file_path: 保存路径

        Returns:
            是否成功
        """
        return PhotoshopHelper.save_document(file_path, 'TIFF')

    @staticmethod
    def create_canvas_and_place_images(arranged_images: list, canvas_width_px: int,
                                     canvas_height_px: int, ppi: int = 72,
                                     log_signal=None, progress_signal=None) -> bool:
        """
        创建画布并放置所有图片 - 统一接口

        Args:
            arranged_images: 已排列的图片列表
            canvas_width_px: 画布宽度（像素）
            canvas_height_px: 画布高度（像素）
            ppi: 分辨率
            log_signal: 日志信号
            progress_signal: 进度信号

        Returns:
            bool: 是否成功
        """
        try:
            if log_signal:
                log_signal.emit(f"开始创建画布: {canvas_width_px}x{canvas_height_px}px, PPI={ppi}")

            # 第一步：创建画布
            canvas_name = f"Canvas_{int(time.time())}"
            success = PhotoshopHelper.create_canvas(
                width=canvas_width_px,
                height=canvas_height_px,
                name=canvas_name,
                ppi=ppi
            )

            if not success:
                if log_signal:
                    log_signal.emit("创建画布失败")
                return False

            if log_signal:
                log_signal.emit(f"画布创建成功，开始放置 {len(arranged_images)} 张图片...")

            # 第二步：放置所有图片
            total_images = len(arranged_images)
            placed_count = 0

            for i, img in enumerate(arranged_images):
                try:
                    # 获取图片信息
                    image_path = img.get('path', '')
                    x = img.get('x', 0)
                    y = img.get('y', 0)
                    width = img.get('width', 0)
                    height = img.get('height', 0)
                    rotation = 90 if img.get('need_rotation', False) or img.get('rotated', False) else 0

                    if not image_path or not os.path.exists(image_path):
                        if log_signal:
                            log_signal.emit(f"跳过不存在的图片: {image_path}")
                        continue

                    # 放置图片
                    success = PhotoshopHelper.place_image(
                        image_path=image_path,
                        x=x,
                        y=y,
                        width=width,
                        height=height,
                        rotation=rotation,
                        ppi=ppi,
                        layer_index=i,  # 传递图层索引
                        total_images=total_images  # 传递总图片数
                    )

                    if success:
                        placed_count += 1
                    else:
                        if log_signal:
                            log_signal.emit(f"放置图片失败: {img.get('name', f'Image_{i+1}')}")

                    # 更新进度
                    if progress_signal and i % 5 == 0:  # 每5张图片更新一次进度
                        progress = int((i + 1) / total_images * 100)
                        progress_signal.emit(progress)

                except Exception as e:
                    if log_signal:
                        log_signal.emit(f"处理图片 {i+1} 时发生错误: {str(e)}")
                    continue

            if log_signal:
                log_signal.emit(f"图片放置完成: {placed_count}/{total_images} 张成功")

            # 最终进度更新
            if progress_signal:
                progress_signal.emit(100)

            return placed_count > 0

        except Exception as e:
            if log_signal:
                log_signal.emit(f"创建画布和放置图片时发生错误: {str(e)}")
            return False

    @staticmethod
    def _parse_version(version_str: str) -> Dict[str, Any]:
        """
        解析Photoshop版本字符串

        Args:
            version_str: Photoshop版本字符串，如"24.0.0"

        Returns:
            包含版本信息的字典
        """
        try:
            # 尝试解析版本号
            version_parts = version_str.split('.')
            major = int(version_parts[0]) if len(version_parts) > 0 else 0
            minor = int(version_parts[1]) if len(version_parts) > 1 else 0
            patch = int(version_parts[2]) if len(version_parts) > 2 else 0

            # 确定版本类型
            version_type = "Unknown"
            if major >= 24:  # Photoshop 2023及以上
                version_type = f"Photoshop {2023 + (major - 24)}"
            elif major >= 22:  # Photoshop 2021及以上
                version_type = f"Photoshop {2021 + (major - 22)}"
            elif major >= 20:  # Photoshop 2019及以上
                version_type = f"Photoshop {2019 + (major - 20)}"
            elif major >= 19:  # Photoshop CC 2018
                version_type = "Photoshop CC 2018"
            elif major >= 18:  # Photoshop CC 2017
                version_type = "Photoshop CC 2017"
            elif major >= 17:  # Photoshop CC 2015.5
                version_type = "Photoshop CC 2015.5"
            elif major >= 16:  # Photoshop CC 2015
                version_type = "Photoshop CC 2015"
            elif major >= 15:  # Photoshop CC 2014
                version_type = "Photoshop CC 2014"
            elif major >= 14:  # Photoshop CC
                version_type = "Photoshop CC"
            elif major >= 13:  # Photoshop CS6
                version_type = "Photoshop CS6"
            elif major >= 12:  # Photoshop CS5
                version_type = "Photoshop CS5"
            elif major >= 11:  # Photoshop CS4
                version_type = "Photoshop CS4"
            elif major >= 10:  # Photoshop CS3
                version_type = "Photoshop CS3"

            return {
                "version": version_str,
                "major": major,
                "minor": minor,
                "patch": patch,
                "type": version_type
            }
        except Exception as e:
            log.error(f"解析版本信息失败: {str(e)}")
            return {
                "version": version_str,
                "major": 0,
                "minor": 0,
                "patch": 0,
                "type": "Unknown"
            }

    @staticmethod
    def _set_compatibility_mode(version_info: Dict[str, Any]) -> None:
        """
        根据Photoshop版本设置兼容性模式

        Args:
            version_info: 版本信息字典
        """
        if not version_info:
            # 默认使用最保守的兼容性模式
            PhotoshopHelper._compatibility_mode = "legacy"
            return

        major = version_info.get("major", 0)

        # 设置兼容性模式
        if major >= 22:  # Photoshop 2021及以上
            # 现代版本，支持大多数JavaScript功能
            PhotoshopHelper._compatibility_mode = "modern"
        elif major >= 19:  # Photoshop CC 2018及以上
            # 中间版本，支持部分现代JavaScript功能
            PhotoshopHelper._compatibility_mode = "intermediate"
        else:  # 旧版本
            # 旧版本，使用最保守的兼容性模式
            PhotoshopHelper._compatibility_mode = "legacy"

    @staticmethod
    def get_compatibility_mode() -> str:
        """
        获取当前的兼容性模式

        Returns:
            兼容性模式字符串: "modern", "intermediate" 或 "legacy"
        """
        if PhotoshopHelper._compatibility_mode is None:
            # 如果尚未设置，尝试检测Photoshop
            PhotoshopHelper.check_photoshop()

        # 如果仍未设置，使用默认值
        if PhotoshopHelper._compatibility_mode is None:
            PhotoshopHelper._compatibility_mode = "legacy"

        return PhotoshopHelper._compatibility_mode

    @staticmethod
    def supports_json() -> bool:
        """
        检查当前Photoshop版本是否支持JSON

        Returns:
            是否支持JSON
        """
        mode = PhotoshopHelper.get_compatibility_mode()
        return mode in ["modern", "intermediate"]

    @staticmethod
    def supports_modern_js() -> bool:
        """
        检查当前Photoshop版本是否支持现代JavaScript特性

        Returns:
            是否支持现代JavaScript
        """
        return PhotoshopHelper.get_compatibility_mode() == "modern"

    @staticmethod
    def purge_memory() -> bool:
        """
        清理Photoshop内存

        Returns:
            是否成功
        """
        try:
            app = ps.Application()

            # 使用JavaScript执行内存清理
            js_script = """
            try {
                // 清理所有缓存
                app.purge(PurgeTarget.ALLCACHES);
                "成功清理内存";
            } catch(e) {
                "清理内存失败: " + e.toString();
            }
            """

            result = app.doJavaScript(js_script)
            log.info(f"内存清理结果: {result}")
            return "成功" in result
        except Exception as e:
            log.error(f"清理内存失败: {str(e)}")
            return False

    @staticmethod
    def close_document(save: bool = False) -> bool:
        """
        关闭当前文档

        Args:
            save: 是否保存更改

        Returns:
            是否成功
        """
        try:
            PhotoshopHelper.log(f"关闭当前文档 (保存更改: {save})")
            app = ps.Application()

            # 使用常量代替硬编码值
            save_option = get_constant('PS_SAVE_CHANGES', 1) if save else get_constant('PS_DONT_SAVE_CHANGES', 2)

            # 使用JavaScript关闭当前文档
            js_script = f"""
            try {{
                if (app.documents.length > 0) {{
                    app.activeDocument.close({save_option});
                    return "成功关闭当前文档";
                }} else {{
                    return "没有打开的文档";
                }}
            }} catch(e) {{
                return "关闭文档失败: " + e.toString();
            }}
            """

            result = app.doJavaScript(js_script)
            PhotoshopHelper.log(f"关闭文档结果: {result}")
            return "成功关闭" in result
        except Exception as e:
            error_message = f"关闭文档失败: {str(e)}"
            log.error(error_message)
            PhotoshopHelper.log(error_message)
            return False

    @staticmethod
    def close_all_documents(save: bool = False) -> bool:
        """
        关闭所有打开的文档

        Args:
            save: 是否保存更改

        Returns:
            是否成功
        """
        try:
            PhotoshopHelper.log(f"关闭所有文档 (保存更改: {save})")
            app = ps.Application()

            # 使用常量代替硬编码值
            save_option = get_constant('PS_SAVE_CHANGES', 1) if save else get_constant('PS_DONT_SAVE_CHANGES', 2)

            # 使用JavaScript关闭所有文档
            js_script = f"""
            try {{
                var docs = app.documents;
                var count = 0;

                while(docs.length > 0) {{
                    docs[0].close({save_option});
                    count++;
                }}

                "成功关闭" + count + "个文档";
            }} catch(e) {{
                "关闭文档失败: " + e.toString();
            }}
            """

            result = app.doJavaScript(js_script)
            PhotoshopHelper.log(f"关闭文档结果: {result}")
            return "成功关闭" in result
        except Exception as e:
            error_message = f"关闭文档失败: {str(e)}"
            log.error(error_message)
            PhotoshopHelper.log(error_message)
            return False

    @staticmethod
    def safe_cleanup_resources() -> bool:
        """
        安全地清理Photoshop资源，包括关闭文档和清理内存
        这个方法会捕获所有异常，确保不会引起应用程序崩溃

        Returns:
            是否成功清理资源
        """
        success = True
        try:
            # 先关闭所有文档
            close_result = PhotoshopHelper.close_all_documents()
            if not close_result:
                log.warning("关闭文档失败，继续尝试清理内存")
                success = False

            # 然后清理内存
            purge_result = PhotoshopHelper.purge_memory()
            if not purge_result:
                log.warning("清理内存失败")
                success = False

            # 尝试使用更强大的清理方法
            try:
                app = ps.Application()
                js_script = """
                try {
                    // 关闭所有文档
                    while(app.documents.length > 0) {
                        app.documents[0].close(SaveOptions.DONOTSAVECHANGES);
                    }

                    // 强制清理内存
                    app.purge(PurgeTarget.ALLCACHES);
                    app.purge(PurgeTarget.HISTORYCACHES);
                    app.purge(PurgeTarget.CLIPBOARDCACHE);
                    app.purge(PurgeTarget.UNDOCACHES);

                    // 强制垃圾回收
                    $.gc();

                    "强制清理成功";
                } catch(e) {
                    "强制清理失败: " + e.toString();
                }
                """
                result = app.doJavaScript(js_script)
                log.info(f"强制清理结果: {result}")
            except Exception as e:
                log.warning(f"强制清理失败: {str(e)}")

            return success
        except Exception as e:
            log.error(f"安全清理资源失败: {str(e)}")
            return False

    @staticmethod
    def execute_js(js_script: str) -> str:
        """执行JavaScript脚本

        Args:
            js_script: JavaScript脚本

        Returns:
            执行结果
        """
        try:
            app = ps.Application()
            result = app.doJavaScript(js_script)
            PhotoshopHelper.log(f"JavaScript执行结果: {result}")
            return result
        except Exception as e:
            error_message = f"执行JavaScript失败: {str(e)}"
            log.error(error_message)
            PhotoshopHelper.log(error_message)
            return error_message

    @staticmethod
    def _save_with_advanced_js(file_path: str, format: str = 'TIFF') -> bool:
        """使用高级JavaScript方法保存当前Photoshop文档

        Args:
            file_path: 保存路径
            format: 文件格式

        Returns:
            是否成功
        """
        try:
            log.info("尝试使用高级JavaScript方法保存文件...")
            app = ps.Application()

            # 确保输出目录存在
            output_dir = os.path.dirname(file_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 使用更强大的JavaScript保存方法
            js_save_script = f"""
            (function() {{
                try {{
                    // 设置无对话框模式
                    app.displayDialogs = DialogModes.NO;

                    // 获取当前文档
                    var doc = app.activeDocument;

                    // 确保文档是RGB模式
                    if (doc.mode != DocumentMode.RGB) {{
                        doc.changeMode(DocumentMode.RGB);
                    }}

                    // 不再自动裁剪文档，保持原始尺寸
                    // 这样可以确保图片位置与预期一致

                    // 创建保存选项
                    var saveOptions;
                    if("{format.upper()}" === "TIFF") {{
                        saveOptions = new TiffSaveOptions();
                        saveOptions.imageCompression = TIFFEncoding.TIFFLZW;
                        saveOptions.embedColorProfile = true;
                        saveOptions.alphaChannels = false;
                        saveOptions.layers = false;
                        saveOptions.transparency = false;
                    }} else {{
                        saveOptions = new JPEGSaveOptions();
                        saveOptions.quality = 12;
                        saveOptions.embedColorProfile = true;
                        saveOptions.formatOptions = FormatOptions.STANDARDBASELINE;
                    }}

                    // 保存文件
                    var fileRef = new File("{file_path.replace('\\', '\\\\')}");
                    doc.saveAs(fileRef, saveOptions, true);

                    return "保存成功 (RGB模式)";
                }} catch(e) {{
                    return "保存失败: " + e.toString();
                }}
            }})();
            """

            result = app.doJavaScript(js_save_script)
            log.info(f"JavaScript保存结果: {result}")

            if "保存成功" in result:
                log.info("使用高级JavaScript方法保存文件成功")
                return True
            else:
                log.error(f"高级JavaScript保存失败: {result}")
                return False

        except Exception as e:
            log.error(f"高级JavaScript保存失败: {str(e)}")
            return False
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片覆盖修复验证测试

验证图层顺序管理、坐标验证等修复的效果

作者: PS画布修复团队
日期: 2024-12-19
版本: 修复验证
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_layer_order_fix():
    """测试图层顺序修复"""
    print("🧪 测试图层顺序修复")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        
        # 创建RectPackArranger实例
        arranger = RectPackArranger(
            container_width=205,
            image_spacing=2,
            max_height=5000
        )
        
        print(f"📋 图层顺序修复测试:")
        
        # 模拟多个图片放置
        test_images = [
            {'name': 'image1.jpg', 'width': 120, 'height': 80},
            {'name': 'image2.jpg', 'width': 80, 'height': 60},
            {'name': 'image3.jpg', 'width': 100, 'height': 70},
            {'name': 'image4.jpg', 'width': 90, 'height': 50}
        ]
        
        placed_results = []
        
        for i, img in enumerate(test_images, 1):
            print(f"\n  放置图片 {i}: {img['name']}")
            
            # 测试图片放置
            x, y, success = arranger.place_image(img['width'], img['height'], img)
            
            if success:
                print(f"    ✅ 放置成功: 位置({x}, {y})")
                placed_results.append({
                    'name': img['name'],
                    'x': x,
                    'y': y,
                    'width': img['width'],
                    'height': img['height'],
                    'index': i-1
                })
            else:
                print(f"    ❌ 放置失败")
        
        # 验证放置结果
        print(f"\n📊 放置结果验证:")
        print(f"  成功放置: {len(placed_results)}/{len(test_images)} 张图片")
        
        # 检查重叠
        overlap_count = 0
        for i, img1 in enumerate(placed_results):
            for j, img2 in enumerate(placed_results[i+1:], i+1):
                if check_overlap(img1, img2):
                    overlap_count += 1
                    print(f"  ⚠️ 发现重叠: {img1['name']} 与 {img2['name']}")
        
        if overlap_count == 0:
            print(f"  ✅ 无重叠检测通过")
        else:
            print(f"  ❌ 发现 {overlap_count} 个重叠")
        
        return overlap_count == 0
        
    except Exception as e:
        print(f"❌ 图层顺序修复测试失败: {str(e)}")
        return False

def check_overlap(img1, img2):
    """检查两个图片是否重叠"""
    x1, y1, w1, h1 = img1['x'], img1['y'], img1['width'], img1['height']
    x2, y2, w2, h2 = img2['x'], img2['y'], img2['width'], img2['height']
    
    # 矩形重叠检测
    return not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1)

def test_coordinate_validation():
    """测试坐标验证功能"""
    print("\n🧪 测试坐标验证功能")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        
        # 创建RectPackArranger实例
        arranger = RectPackArranger(
            container_width=205,
            image_spacing=2,
            max_height=5000
        )
        
        print(f"📋 坐标验证测试:")
        
        # 测试用例
        test_cases = [
            {
                'name': '正常坐标',
                'x': 10, 'y': 10, 'width': 100, 'height': 80,
                'expected': True
            },
            {
                'name': '负坐标',
                'x': -5, 'y': 10, 'width': 100, 'height': 80,
                'expected': False
            },
            {
                'name': '超出右边界',
                'x': 150, 'y': 10, 'width': 100, 'height': 80,
                'expected': False
            },
            {
                'name': '超出下边界',
                'x': 10, 'y': 4950, 'width': 100, 'height': 80,
                'expected': False
            }
        ]
        
        all_passed = True
        
        for i, test in enumerate(test_cases, 1):
            is_valid, error_msg = arranger._validate_image_placement(
                test['x'], test['y'], test['width'], test['height']
            )
            
            expected = test['expected']
            passed = is_valid == expected
            
            print(f"  测试 {i}: {'✅ 通过' if passed else '❌ 失败'} - {test['name']}")
            print(f"    坐标: ({test['x']}, {test['y']}, {test['width']}, {test['height']})")
            print(f"    结果: {'有效' if is_valid else '无效'}")
            print(f"    预期: {'有效' if expected else '无效'}")
            if not is_valid:
                print(f"    错误: {error_msg}")
            
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 坐标验证测试失败: {str(e)}")
        return False

def test_overlap_detection():
    """测试重叠检测功能"""
    print("\n🧪 测试重叠检测功能")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        
        # 创建RectPackArranger实例
        arranger = RectPackArranger(
            container_width=205,
            image_spacing=2,
            max_height=5000
        )
        
        print(f"📋 重叠检测测试:")
        
        # 先放置一个图片
        placed_img = {'x': 50, 'y': 50, 'width': 100, 'height': 80, 'name': 'base_image'}
        arranger.placed_images.append(placed_img)
        
        # 测试用例
        test_cases = [
            {
                'name': '不重叠（左侧）',
                'x': 10, 'y': 50, 'width': 30, 'height': 80,
                'expected_overlap': False
            },
            {
                'name': '不重叠（右侧）',
                'x': 160, 'y': 50, 'width': 30, 'height': 80,
                'expected_overlap': False
            },
            {
                'name': '不重叠（上方）',
                'x': 50, 'y': 10, 'width': 100, 'height': 30,
                'expected_overlap': False
            },
            {
                'name': '不重叠（下方）',
                'x': 50, 'y': 140, 'width': 100, 'height': 30,
                'expected_overlap': False
            },
            {
                'name': '重叠（中心）',
                'x': 75, 'y': 75, 'width': 50, 'height': 40,
                'expected_overlap': True
            },
            {
                'name': '重叠（边缘）',
                'x': 140, 'y': 120, 'width': 50, 'height': 40,
                'expected_overlap': True
            }
        ]
        
        all_passed = True
        
        for i, test in enumerate(test_cases, 1):
            has_overlap = arranger._check_overlap(
                test['x'], test['y'], test['width'], test['height'], placed_img
            )
            
            expected = test['expected_overlap']
            passed = has_overlap == expected
            
            print(f"  测试 {i}: {'✅ 通过' if passed else '❌ 失败'} - {test['name']}")
            print(f"    新图片: ({test['x']}, {test['y']}, {test['width']}, {test['height']})")
            print(f"    基准图片: ({placed_img['x']}, {placed_img['y']}, {placed_img['width']}, {placed_img['height']})")
            print(f"    检测结果: {'重叠' if has_overlap else '不重叠'}")
            print(f"    预期结果: {'重叠' if expected else '不重叠'}")
            
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 重叠检测测试失败: {str(e)}")
        return False

def test_layer_index_calculation():
    """测试图层索引计算修复"""
    print("\n🧪 测试图层索引计算修复")
    print("=" * 80)
    
    try:
        from core.rectpack_arranger import RectPackArranger
        
        # 创建RectPackArranger实例
        arranger = RectPackArranger(
            container_width=205,
            image_spacing=2,
            max_height=5000
        )
        
        print(f"📋 图层索引计算测试:")
        
        # 模拟图片放置过程中的索引计算
        test_images = [
            {'name': 'image1.jpg', 'width': 120, 'height': 80},
            {'name': 'image2.jpg', 'width': 80, 'height': 60},
            {'name': 'image3.jpg', 'width': 100, 'height': 70}
        ]
        
        expected_indices = [0, 1, 2]  # 预期的图层索引
        actual_indices = []
        
        for i, img in enumerate(test_images):
            # 记录放置前的placement_count
            current_placement_count = arranger.placement_count
            actual_indices.append(current_placement_count)
            
            print(f"  图片 {i+1}: {img['name']}")
            print(f"    放置前placement_count: {current_placement_count}")
            print(f"    预期图层索引: {expected_indices[i]}")
            print(f"    实际图层索引: {current_placement_count}")
            
            # 放置图片
            x, y, success = arranger.place_image(img['width'], img['height'], img)
            
            if success:
                print(f"    ✅ 放置成功，placement_count更新为: {arranger.placement_count}")
            else:
                print(f"    ❌ 放置失败")
                break
        
        # 验证索引计算是否正确
        indices_correct = actual_indices == expected_indices
        
        print(f"\n📊 索引计算验证:")
        print(f"  预期索引: {expected_indices}")
        print(f"  实际索引: {actual_indices}")
        print(f"  索引正确: {'✅ 是' if indices_correct else '❌ 否'}")
        
        return indices_correct
        
    except Exception as e:
        print(f"❌ 图层索引计算测试失败: {str(e)}")
        return False

def generate_overlap_fix_test_report():
    """生成图片覆盖修复测试报告"""
    print("\n📊 图片覆盖修复测试报告")
    print("=" * 80)
    
    # 执行所有测试
    test_results = {
        'layer_order_fix': test_layer_order_fix(),
        'coordinate_validation': test_coordinate_validation(),
        'overlap_detection': test_overlap_detection(),
        'layer_index_calculation': test_layer_index_calculation()
    }
    
    # 统计结果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    print(f"\n📋 测试结果汇总:")
    print(f"  图层顺序修复: {'✅ 通过' if test_results['layer_order_fix'] else '❌ 失败'}")
    print(f"  坐标验证功能: {'✅ 通过' if test_results['coordinate_validation'] else '❌ 失败'}")
    print(f"  重叠检测功能: {'✅ 通过' if test_results['overlap_detection'] else '❌ 失败'}")
    print(f"  图层索引计算: {'✅ 通过' if test_results['layer_index_calculation'] else '❌ 失败'}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print(f"🎉 图片覆盖修复测试全部通过！")
        print(f"✅ 关键修复已生效")
        
        # 修复效果总结
        print(f"\n🎯 修复效果总结:")
        print(f"  1. ✅ 图层顺序：删除错误的图层移动代码，保持自然堆叠")
        print(f"  2. ✅ 坐标验证：添加边界检查和重叠检测")
        print(f"  3. ✅ 重叠检测：准确识别图片重叠情况")
        print(f"  4. ✅ 索引计算：使用正确的placement_count作为图层索引")
        
        print(f"\n🚀 预期改善:")
        print(f"  • 消除图片覆盖问题")
        print(f"  • 消除空白区域")
        print(f"  • 图片按算法预期位置排列")
        print(f"  • 提高布局准确性")
        
    else:
        print(f"⚠️ 图片覆盖修复测试存在问题，需要进一步调试")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🔧 图片覆盖修复验证测试")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证图层顺序管理修复效果")
    print("2. 🔍 验证坐标验证功能")
    print("3. 🔍 验证重叠检测功能")
    print("4. 🔍 验证图层索引计算修复")
    print("=" * 100)
    
    # 执行测试
    success = generate_overlap_fix_test_report()
    
    if success:
        print(f"\n🎯 修复验证结果:")
        print(f"✅ 所有关键修复都已生效")
        print(f"🚀 可以在实际环境中测试修复效果")
        print(f"📊 预期解决图片覆盖和空白区域问题")
    else:
        print(f"\n🔧 需要进一步调试:")
        print(f"1. 检查失败的测试用例")
        print(f"2. 完善修复逻辑")
        print(f"3. 重新验证修复效果")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

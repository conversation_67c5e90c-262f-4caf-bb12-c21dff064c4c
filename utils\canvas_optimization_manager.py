#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
画布优化管理器模块

集成所有画布优化功能：
1. 底部行优化
2. 画布截断优化
3. 画布利用率分析
4. 多次优化尝试
"""

import logging
import copy
import time
from typing import List, Dict, Any, Tuple, Optional

# 导入优化器模块
from utils.bottom_row_optimizer import BottomRowOptimizer
from utils.canvas_truncation_optimizer import CanvasTruncationOptimizer
from utils.canvas_utilization_analyzer import CanvasUtilizationAnalyzer
from utils.tetris_enhanced_optimizer import TetrisEnhancedOptimizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("CanvasOptimizationManager")

class CanvasOptimizationManager:
    """
    画布优化管理器，集成所有画布优化功能
    
    特性：
    1. 底部行优化
    2. 画布截断优化
    3. 画布利用率分析
    4. 多次优化尝试
    """
    
    def __init__(self, tetris_packer=None):
        """
        初始化画布优化管理器
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        
        # 创建优化器实例
        self.bottom_row_optimizer = BottomRowOptimizer(tetris_packer)
        self.truncation_optimizer = CanvasTruncationOptimizer(tetris_packer)
        self.utilization_analyzer = CanvasUtilizationAnalyzer(tetris_packer)
        self.enhanced_optimizer = TetrisEnhancedOptimizer(tetris_packer)
        
        log.info("画布优化管理器初始化完成")
    
    def optimize_canvas(self, tetris_packer=None, remaining_patterns: List[Dict[str, Any]] = None, optimization_level: int = 2) -> Dict[str, Any]:
        """
        执行画布优化
        
        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            remaining_patterns: 剩余未放置的图片列表
            optimization_level: 优化级别，1=基础，2=中等，3=高级
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        packer = tetris_packer or self.tetris_packer
        if not packer:
            log.error("未提供Tetris算法实例")
            return {'success': False, 'message': "未提供Tetris算法实例"}
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 记录原始状态
            original_placed_images = copy.deepcopy(packer.placed_images)
            original_max_height = packer.get_max_height()
            original_utilization = packer.get_utilization()
            
            log.info(f"开始画布优化，原始高度: {original_max_height}, 原始利用率: {original_utilization:.4f}, 优化级别: {optimization_level}")
            
            # 执行优化
            optimization_results = []
            
            # 1. 底部行优化
            if packer.placed_images and remaining_patterns:
                bottom_row_result = self.bottom_row_optimizer.optimize_bottom_row(packer, remaining_patterns)
                optimization_results.append({
                    'step': "底部行优化",
                    'success': bottom_row_result,
                    'utilization': packer.get_utilization()
                })
            
            # 2. 画布截断前优化
            if packer.placed_images and remaining_patterns:
                truncation_result = self.truncation_optimizer.optimize_before_truncation(packer, remaining_patterns)
                optimization_results.append({
                    'step': "画布截断前优化",
                    'success': truncation_result.get('success', False),
                    'utilization': packer.get_utilization(),
                    'details': truncation_result
                })
            
            # 3. 根据优化级别执行更多优化
            if optimization_level >= 2:
                # 执行中等级别优化
                enhanced_result = self.enhanced_optimizer.optimize(packer, optimization_level)
                optimization_results.append({
                    'step': "增强优化",
                    'success': enhanced_result.get('success', False),
                    'utilization': packer.get_utilization(),
                    'details': enhanced_result
                })
            
            # 计算最终状态
            final_max_height = packer.get_max_height()
            final_utilization = packer.get_utilization()
            
            # 计算改进百分比
            height_reduction = (original_max_height - final_max_height) / original_max_height * 100 if original_max_height > 0 else 0
            utilization_improvement = (final_utilization - original_utilization) / original_utilization * 100 if original_utilization > 0 else 0
            
            # 判断是否应将底部行图片移至下一画布
            should_move_bottom = self.truncation_optimizer.should_move_bottom_row(packer)
            
            # 获取优化建议
            suggestions = self.utilization_analyzer.get_optimization_suggestions(packer)
            
            # 记录结束时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 返回优化结果
            return {
                'success': any(result['success'] for result in optimization_results),
                'original_height': original_max_height,
                'final_height': final_max_height,
                'original_utilization': original_utilization,
                'final_utilization': final_utilization,
                'height_reduction': height_reduction,
                'utilization_improvement': utilization_improvement,
                'should_move_bottom_row': should_move_bottom,
                'suggestions': suggestions,
                'execution_time': execution_time,
                'optimization_steps': optimization_results,
                'message': "画布优化完成"
            }
            
        except Exception as e:
            log.error(f"画布优化失败: {str(e)}")
            # 恢复原始状态
            if 'original_placed_images' in locals():
                packer.placed_images = original_placed_images
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            return {'success': False, 'message': f"画布优化失败: {str(e)}"}
    
    def optimize_multiple_canvases(self, tetris_packers: List[Any], remaining_patterns: List[Dict[str, Any]] = None, optimization_level: int = 2) -> Dict[str, Any]:
        """
        优化多个画布
        
        Args:
            tetris_packers: Tetris算法实例列表
            remaining_patterns: 剩余未放置的图片列表
            optimization_level: 优化级别，1=基础，2=中等，3=高级
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        if not tetris_packers:
            log.error("未提供Tetris算法实例列表")
            return {'success': False, 'message': "未提供Tetris算法实例列表"}
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 优化每个画布
            canvas_results = []
            overall_improvement = 0.0
            
            for i, packer in enumerate(tetris_packers):
                log.info(f"优化画布 {i+1}/{len(tetris_packers)}")
                
                # 优化当前画布
                result = self.optimize_canvas(packer, remaining_patterns, optimization_level)
                canvas_results.append(result)
                
                # 累计改进百分比
                if result['success']:
                    overall_improvement += result['utilization_improvement']
            
            # 计算平均改进百分比
            avg_improvement = overall_improvement / len(tetris_packers) if tetris_packers else 0.0
            
            # 记录结束时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 返回优化结果
            return {
                'success': any(result['success'] for result in canvas_results),
                'canvas_count': len(tetris_packers),
                'overall_improvement': overall_improvement,
                'average_improvement': avg_improvement,
                'execution_time': execution_time,
                'canvas_results': canvas_results,
                'message': "多画布优化完成"
            }
            
        except Exception as e:
            log.error(f"多画布优化失败: {str(e)}")
            return {'success': False, 'message': f"多画布优化失败: {str(e)}"}
            
# 使用示例
# manager = CanvasOptimizationManager(tetris_packer)
# result = manager.optimize_canvas(remaining_patterns=remaining_patterns, optimization_level=3)
# print(f"优化结果: 利用率从 {result['original_utilization']:.4f} 提高到 {result['final_utilization']:.4f}, 提升 {result['utilization_improvement']:.2f}%")

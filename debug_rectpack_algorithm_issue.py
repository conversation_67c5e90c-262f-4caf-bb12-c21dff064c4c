#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack算法问题深度分析脚本

深入分析RectPack算法在正式环境中为什么只是简单依次排列，
而不是按照RectPack算法的优化布局

作者: RectPack算法调试团队
日期: 2024-12-19
版本: 深度分析版
"""

import sys
import os
import time
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_rectpack_algorithm_flow():
    """
    分析RectPack算法的完整调用流程
    """
    print("🔍 分析RectPack算法的完整调用流程")
    print("=" * 80)

    try:
        # 检查RectPack库是否可用
        try:
            from rectpack import newPacker, SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO
            from rectpack.binpack import BinPack
            rectpack_available = True
            print("✅ RectPack库可用")
        except ImportError as e:
            rectpack_available = False
            print(f"❌ RectPack库不可用: {str(e)}")

        # 检查RectPackArranger的初始化
        from core.rectpack_arranger import RectPackArranger, RECTPACK_AVAILABLE

        print(f"\n📋 RectPackArranger状态:")
        print(f"  RECTPACK_AVAILABLE: {RECTPACK_AVAILABLE}")
        print(f"  实际库可用性: {rectpack_available}")

        if not RECTPACK_AVAILABLE:
            print("⚠️ 警告: RectPack库不可用，将使用简化算法")
            print("这是导致只有简单依次排列的根本原因！")
            return False

        # 创建测试数据
        test_images = [
            {'width': 120, 'height': 80, 'name': 'img1'},
            {'width': 80, 'height': 60, 'name': 'img2'},
            {'width': 100, 'height': 70, 'name': 'img3'},
            {'width': 90, 'height': 50, 'name': 'img4'},
        ]

        print(f"\n🔧 测试RectPack算法:")
        print(f"  测试图片数量: {len(test_images)}")

        # 创建RectPackArranger
        arranger = RectPackArranger(
            container_width=205,
            image_spacing=2,
            max_height=500
        )

        print(f"  画布尺寸: 205x500px")
        print(f"  图片间距: 2px")

        # 测试每个图片的放置
        placed_images = []
        for i, img in enumerate(test_images):
            print(f"\n  放置图片 {i+1}: {img['name']} ({img['width']}x{img['height']}px)")

            x, y, success = arranger.place_image(img['width'], img['height'], img)

            if success:
                print(f"    ✅ 成功放置在 ({x}, {y})")
                placed_images.append({
                    'name': img['name'],
                    'x': x,
                    'y': y,
                    'width': img['width'],
                    'height': img['height']
                })
            else:
                print(f"    ❌ 放置失败")

        # 分析布局结果
        print(f"\n📊 布局结果分析:")
        print(f"  成功放置: {len(placed_images)} 张")

        if placed_images:
            # 检查是否是简单的依次排列
            is_simple_sequential = True
            prev_x = -1
            prev_y = -1

            for img in placed_images:
                print(f"    {img['name']}: 位置({img['x']}, {img['y']}) 尺寸({img['width']}x{img['height']})")

                # 检查是否是简单的从左到右，从上到下排列
                if img['y'] == prev_y and img['x'] <= prev_x:
                    is_simple_sequential = False
                elif img['y'] < prev_y:
                    is_simple_sequential = False

                prev_x = img['x']
                prev_y = img['y']

            if is_simple_sequential:
                print("  ⚠️ 检测到简单的依次排列模式")
            else:
                print("  ✅ 检测到优化的RectPack布局模式")

        # 获取布局统计
        layout_info = arranger.get_layout_info()
        print(f"\n📈 布局统计:")
        print(f"  画布利用率: {layout_info['utilization_percent']:.2f}%")
        print(f"  实际画布高度: {layout_info['container_height']}px")
        print(f"  已用面积: {layout_info['used_area']}px²")

        return True

    except Exception as e:
        print(f"❌ RectPack算法流程分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_unified_image_arranger():
    """
    分析UnifiedImageArranger的调用逻辑
    """
    print("\n🔍 分析UnifiedImageArranger的调用逻辑")
    print("=" * 80)

    try:
        from core.unified_image_arranger import UnifiedImageArranger

        # 创建测试数据
        pattern_items = [
            {
                'pattern_name': 'test1',
                'width_cm': 12,
                'height_cm': 8,
                'quantity': 1,
                'path': '/fake/path/test1.jpg',
                'index': 0,
                'row_number': 1
            },
            {
                'pattern_name': 'test2',
                'width_cm': 8,
                'height_cm': 6,
                'quantity': 1,
                'path': '/fake/path/test2.jpg',
                'index': 1,
                'row_number': 2
            },
            {
                'pattern_name': 'test3',
                'width_cm': 10,
                'height_cm': 7,
                'quantity': 1,
                'path': '/fake/path/test3.jpg',
                'index': 2,
                'row_number': 3
            }
        ]

        print(f"📋 测试数据: {len(pattern_items)} 张图片")

        # 创建UnifiedImageArranger
        arranger = UnifiedImageArranger()

        # 初始化
        canvas_width_px = 205
        max_height_px = 500
        image_spacing_px = 2
        ppi = 72

        arranger.initialize(
            canvas_width_px=canvas_width_px,
            max_height_px=max_height_px,
            image_spacing_px=image_spacing_px,
            ppi=ppi
        )

        print(f"🎨 画布参数: {canvas_width_px}x{max_height_px}px, 间距: {image_spacing_px}px")

        # 执行排列
        print(f"\n🔧 执行图片排列...")
        arranged_images = arranger.arrange_images(pattern_items)

        print(f"📊 排列结果:")
        print(f"  输入图片数: {len(pattern_items)}")
        print(f"  成功排列数: {len(arranged_images)}")

        # 分析arranged_images的数据结构
        if arranged_images:
            print(f"\n📋 arranged_images数据结构分析:")
            for i, img in enumerate(arranged_images, 1):
                print(f"  图片 {i}: {img.get('name', 'Unknown')}")
                print(f"    坐标: ({img.get('x', 'N/A')}, {img.get('y', 'N/A')})")
                print(f"    尺寸: {img.get('width', 'N/A')}x{img.get('height', 'N/A')}px")
                print(f"    旋转: {img.get('need_rotation', False) or img.get('rotated', False)}")
                print(f"    路径: {img.get('path', 'N/A')}")

                # 检查关键字段是否存在
                required_fields = ['x', 'y', 'width', 'height', 'path', 'name']
                missing_fields = [field for field in required_fields if field not in img]
                if missing_fields:
                    print(f"    ⚠️ 缺失字段: {missing_fields}")
                else:
                    print(f"    ✅ 数据结构完整")

        # 获取布局统计
        stats = arranger.get_layout_statistics()
        if stats:
            print(f"\n📈 布局统计:")
            print(f"  画布利用率: {stats.get('utilization_percent', 0):.2f}%")
            print(f"  画布尺寸: {stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")

        return arranged_images

    except Exception as e:
        print(f"❌ UnifiedImageArranger分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def analyze_rectpack_layout_worker():
    """
    分析RectPackLayoutWorker的PS调用逻辑
    """
    print("\n🔍 分析RectPackLayoutWorker的PS调用逻辑")
    print("=" * 80)

    try:
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        import inspect

        # 检查_create_photoshop_canvas方法
        method_source = inspect.getsource(RectPackLayoutWorker._create_photoshop_canvas)

        print("📋 检查_create_photoshop_canvas方法:")

        # 检查关键逻辑
        checks = [
            ("使用arranged_images", "arranged_images" in method_source),
            ("调用image_processor.place_image", "image_processor.place_image" in method_source),
            ("传递坐标信息", "'x':" in method_source and "'y':" in method_source),
            ("传递尺寸信息", "'width':" in method_source and "'height':" in method_source),
            ("传递旋转信息", "'rotated':" in method_source),
            ("循环处理所有图片", "for" in method_source and "arranged_images" in method_source)
        ]

        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")

        # 检查place_info的构建逻辑
        if "place_info = {" in method_source:
            print("  ✅ 正确构建place_info字典")

            # 提取place_info的构建部分
            lines = method_source.split('\n')
            in_place_info = False
            place_info_lines = []

            for line in lines:
                if "place_info = {" in line:
                    in_place_info = True
                    place_info_lines.append(line.strip())
                elif in_place_info:
                    place_info_lines.append(line.strip())
                    if "}" in line and not line.strip().endswith(","):
                        break

            print("  📋 place_info构建逻辑:")
            for line in place_info_lines[:10]:  # 只显示前10行
                print(f"    {line}")
        else:
            print("  ❌ 未找到place_info构建逻辑")

        return True

    except Exception as e:
        print(f"❌ RectPackLayoutWorker分析失败: {str(e)}")
        return False

def analyze_image_processor():
    """
    分析image_processor的place_image方法
    """
    print("\n🔍 分析image_processor的place_image方法")
    print("=" * 80)

    try:
        from utils.image_processor import PhotoshopImageProcessor
        import inspect

        # 检查place_image方法
        method_source = inspect.getsource(PhotoshopImageProcessor.place_image)

        print("📋 检查PhotoshopImageProcessor.place_image方法:")

        # 检查关键逻辑
        checks = [
            ("提取坐标信息", "image_info.get('x'" in method_source),
            ("提取尺寸信息", "image_info.get('width'" in method_source),
            ("提取旋转信息", "image_info.get('rotated'" in method_source),
            ("调用PhotoshopHelper", "self.ps_helper.place_image" in method_source),
            ("传递精确坐标", "x=x" in method_source and "y=y" in method_source),
            ("传递精确尺寸", "width=width" in method_source and "height=height" in method_source),
            ("处理旋转角度", "rotation_angle" in method_source)
        ]

        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"  {status} {check_name}")

        # 检查是否有RectPack专用注释
        if "RectPack算法" in method_source:
            print("  ✅ 包含RectPack算法专用逻辑")
        else:
            print("  ⚠️ 未发现RectPack算法专用标识")

        return True

    except Exception as e:
        print(f"❌ image_processor分析失败: {str(e)}")
        return False

def simulate_complete_flow():
    """
    模拟完整的RectPack算法流程
    """
    print("\n🔧 模拟完整的RectPack算法流程")
    print("=" * 80)

    try:
        # 第一步：检查RectPack库
        from core.rectpack_arranger import RECTPACK_AVAILABLE

        print(f"第一步：RectPack库检查")
        print(f"  RECTPACK_AVAILABLE: {RECTPACK_AVAILABLE}")

        if not RECTPACK_AVAILABLE:
            print("  ❌ 关键问题：RectPack库不可用！")
            print("  这会导致RectPackArranger使用_simple_place_image方法")
            print("  _simple_place_image只是简单的从左到右，从上到下排列")
            print("  这就是为什么看不到RectPack算法效果的根本原因！")
            return False

        # 第二步：模拟UnifiedImageArranger.arrange_images
        print(f"\n第二步：UnifiedImageArranger.arrange_images")
        arranged_images = analyze_unified_image_arranger()

        if not arranged_images:
            print("  ❌ arranged_images为空，无法继续")
            return False

        # 第三步：模拟RectPackLayoutWorker._create_photoshop_canvas
        print(f"\n第三步：RectPackLayoutWorker._create_photoshop_canvas")

        # 模拟place_info的构建
        for i, image_info in enumerate(arranged_images[:2]):  # 只处理前2个
            print(f"  处理图片 {i+1}: {image_info.get('name', 'Unknown')}")

            # 模拟RectPackLayoutWorker的数据提取逻辑
            image_path = image_info.get('path', '')
            x = image_info.get('x', 0)
            y = image_info.get('y', 0)
            width = image_info.get('width', 0)
            height = image_info.get('height', 0)
            need_rotation = image_info.get('need_rotation', False) or image_info.get('rotated', False)
            image_name = image_info.get('name', os.path.basename(image_path))
            image_class = image_info.get('image_class', 'C')

            place_info = {
                'image_path': image_path,
                'x': x,
                'y': y,
                'width': width,
                'height': height,
                'rotated': need_rotation,
                'name': image_name,
                'image_class': image_class
            }

            print(f"    构建的place_info:")
            print(f"      坐标: ({x}, {y})")
            print(f"      尺寸: {width}x{height}")
            print(f"      旋转: {need_rotation}")

            # 检查坐标是否合理
            if x == 0 and y == 0 and i > 0:
                print(f"    ⚠️ 警告: 多个图片都在(0,0)位置，可能存在问题")
            elif x >= 0 and y >= 0:
                print(f"    ✅ 坐标合理")
            else:
                print(f"    ❌ 坐标异常")

        return True

    except Exception as e:
        print(f"❌ 完整流程模拟失败: {str(e)}")
        return False

def identify_root_causes():
    """
    识别根本原因
    """
    print("\n🎯 识别根本原因")
    print("=" * 80)

    potential_issues = []

    # 检查1: RectPack库可用性
    try:
        from core.rectpack_arranger import RECTPACK_AVAILABLE
        if not RECTPACK_AVAILABLE:
            potential_issues.append({
                'type': '关键问题',
                'description': 'RectPack库不可用，导致使用简化算法',
                'impact': '高',
                'solution': '安装rectpack库: pip install rectpack'
            })
    except:
        potential_issues.append({
            'type': '关键问题',
            'description': 'RectPackArranger模块导入失败',
            'impact': '高',
            'solution': '检查core.rectpack_arranger模块'
        })

    # 检查2: 算法选择逻辑
    try:
        from core.unified_image_arranger import UnifiedImageArranger
        arranger = UnifiedImageArranger()
        if not hasattr(arranger, 'rectpack_arranger'):
            potential_issues.append({
                'type': '配置问题',
                'description': 'UnifiedImageArranger未正确初始化RectPackArranger',
                'impact': '中',
                'solution': '检查UnifiedImageArranger.initialize方法'
            })
    except Exception as e:
        potential_issues.append({
            'type': '模块问题',
            'description': f'UnifiedImageArranger初始化失败: {str(e)}',
            'impact': '高',
            'solution': '检查UnifiedImageArranger模块'
        })

    # 检查3: 数据传递链路
    try:
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        from utils.image_processor import PhotoshopImageProcessor
        print("✅ 数据传递链路模块可用")
    except Exception as e:
        potential_issues.append({
            'type': '模块问题',
            'description': f'数据传递链路模块导入失败: {str(e)}',
            'impact': '高',
            'solution': '检查相关模块导入'
        })

    # 输出问题分析
    print(f"📊 发现 {len(potential_issues)} 个潜在问题:")

    for i, issue in enumerate(potential_issues, 1):
        print(f"\n  问题 {i}: {issue['type']}")
        print(f"    描述: {issue['description']}")
        print(f"    影响程度: {issue['impact']}")
        print(f"    解决方案: {issue['solution']}")

    if not potential_issues:
        print("  ✅ 未发现明显的配置问题")
        print("  问题可能在于:")
        print("    1. RectPack算法的参数配置")
        print("    2. 图片数据的预处理")
        print("    3. PS环境的特殊情况")

    return potential_issues

def main():
    """
    主分析函数
    """
    print("🔬 RectPack算法问题深度分析")
    print("=" * 100)
    print("分析目标:")
    print("1. 🔍 检查RectPack算法是否正确生成布局数据")
    print("2. 🔍 验证数据是否正确传递到PS")
    print("3. 🔍 找出为什么PS中只是简单依次排列")
    print("4. 🔍 识别所有问题并提供修复方案")
    print("=" * 100)

    # 执行分析
    results = []

    # 分析1: RectPack算法流程
    result1 = analyze_rectpack_algorithm_flow()
    results.append(("RectPack算法流程", result1))

    # 分析2: UnifiedImageArranger
    arranged_images = analyze_unified_image_arranger()
    results.append(("UnifiedImageArranger", len(arranged_images) > 0))

    # 分析3: RectPackLayoutWorker
    result3 = analyze_rectpack_layout_worker()
    results.append(("RectPackLayoutWorker", result3))

    # 分析4: image_processor
    result4 = analyze_image_processor()
    results.append(("image_processor", result4))

    # 分析5: 完整流程模拟
    result5 = simulate_complete_flow()
    results.append(("完整流程模拟", result5))

    # 识别根本原因
    issues = identify_root_causes()

    # 输出分析结果
    print("\n" + "=" * 100)
    print("📊 深度分析结果汇总:")
    print("=" * 100)

    passed_count = 0
    for test_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1

    print(f"\n📈 总体结果: {passed_count}/{len(results)} 项分析正常")

    # 输出关键发现
    print(f"\n🎯 关键发现:")
    if len(issues) > 0:
        critical_issues = [issue for issue in issues if issue['impact'] == '高']
        if critical_issues:
            print(f"🔥 发现 {len(critical_issues)} 个关键问题:")
            for issue in critical_issues:
                print(f"  • {issue['description']}")
                print(f"    解决方案: {issue['solution']}")
        else:
            print("⚠️ 发现一些配置问题，但不是关键问题")
    else:
        print("✅ 未发现明显的配置问题")
        print("问题可能在于算法参数或PS环境")

    print(f"\n💡 修复建议:")
    print("1. 🔥 优先检查RectPack库是否正确安装和可用")
    print("2. 🔧 验证RECTPACK_AVAILABLE标志是否为True")
    print("3. 🔍 检查UnifiedImageArranger是否正确使用RectPack算法")
    print("4. 📊 验证arranged_images的数据结构是否包含正确的坐标")
    print("5. 🎨 确认PS环境能够正确接收和处理坐标信息")

    return passed_count == len(results) and len(issues) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段优化测试

测试坐标精度统一和图片尺寸预验证的效果

作者: PS画布修复团队
日期: 2024-12-19
版本: 第三阶段测试
"""

import sys
import os
import tempfile
from PIL import Image

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_coordinate_precision_unifier():
    """测试坐标精度统一器"""
    print("🧪 测试坐标精度统一器")
    print("=" * 80)
    
    try:
        from core.coordinate_precision_unifier import (
            CoordinatePrecisionUnifier,
            CoordinateConfig,
            PrecisionLevel,
            CoordinateRect
        )
        
        # 创建配置
        config = CoordinateConfig(
            precision_level=PrecisionLevel.INTEGER,
            rounding_method="round",
            tolerance=2.0
        )
        
        unifier = CoordinatePrecisionUnifier(config)
        
        print(f"📋 坐标精度统一器测试:")
        
        # 测试用例
        test_cases = [
            {
                'name': '浮点数坐标',
                'input': {'x': 10.7, 'y': 20.3, 'width': 100.9, 'height': 80.1},
                'expected': {'x': 11, 'y': 20, 'width': 101, 'height': 80}
            },
            {
                'name': '负坐标处理',
                'input': {'x': -5.2, 'y': 10.8, 'width': 100, 'height': 80},
                'expected': {'x': -5, 'y': 11, 'width': 100, 'height': 80}
            },
            {
                'name': '零尺寸处理',
                'input': {'x': 10, 'y': 20, 'width': 0.5, 'height': 0.3},
                'expected': {'x': 10, 'y': 20, 'width': 1, 'height': 1}  # 自动修正为最小值
            }
        ]
        
        all_passed = True
        
        for i, test in enumerate(test_cases, 1):
            result = unifier.process_image_coordinates(test['input'], 205, 5000)
            
            # 检查关键坐标
            x_match = result['x'] == test['expected']['x']
            y_match = result['y'] == test['expected']['y']
            w_match = result['width'] == test['expected']['width']
            h_match = result['height'] == test['expected']['height']
            
            test_passed = x_match and y_match and w_match and h_match
            
            print(f"  测试 {i}: {'✅ 通过' if test_passed else '❌ 失败'} - {test['name']}")
            print(f"    输入: ({test['input']['x']}, {test['input']['y']}, {test['input']['width']}, {test['input']['height']})")
            print(f"    输出: ({result['x']}, {result['y']}, {result['width']}, {result['height']})")
            print(f"    预期: ({test['expected']['x']}, {test['expected']['y']}, {test['expected']['width']}, {test['expected']['height']})")
            print(f"    统一标记: {result.get('coordinate_unified', False)}")
            print(f"    有效性: {result.get('coordinate_valid', False)}")
            
            if not test_passed:
                all_passed = False
        
        # 获取统计信息
        stats = unifier.get_processing_statistics()
        print(f"\n📊 处理统计:")
        print(f"  处理数量: {stats['processed_count']}")
        print(f"  有效数量: {stats['valid_count']}")
        print(f"  成功率: {stats['success_rate']:.1f}%")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 坐标精度统一器测试失败: {str(e)}")
        return False

def test_image_size_validator():
    """测试图片尺寸验证器"""
    print("\n🧪 测试图片尺寸验证器")
    print("=" * 80)
    
    try:
        from core.image_size_validator import (
            ImageSizeValidator,
            ValidationConfig
        )
        
        # 创建临时测试图片
        temp_dir = tempfile.mkdtemp()
        test_images = []
        
        # 创建测试图片文件
        for i, (width, height) in enumerate([(120, 80), (100, 60), (150, 100)], 1):
            img_path = os.path.join(temp_dir, f"test_image_{i}.jpg")
            img = Image.new('RGB', (width, height), color='red')
            img.save(img_path, 'JPEG')
            test_images.append({
                'name': f'test_image_{i}.jpg',
                'path': img_path,
                'width': width,
                'height': height
            })
        
        # 添加一个尺寸不匹配的测试
        test_images.append({
            'name': 'test_image_4.jpg',
            'path': test_images[0]['path'],  # 使用第一个图片的路径
            'width': 200,  # 但期望不同的尺寸
            'height': 150
        })
        
        # 添加一个不存在的文件
        test_images.append({
            'name': 'nonexistent.jpg',
            'path': '/nonexistent/path/image.jpg',
            'width': 100,
            'height': 80
        })
        
        # 创建验证器
        config = ValidationConfig(
            tolerance_pixels=5,
            tolerance_percent=5.0,
            enable_auto_correction=True
        )
        
        validator = ImageSizeValidator(config)
        
        print(f"📋 图片尺寸验证器测试:")
        print(f"  测试图片: {len(test_images)} 个")
        
        # 批量验证
        validation_results = validator.batch_validate_sizes(test_images)
        
        # 检查验证结果
        for i, (img_data, result) in enumerate(zip(test_images, validation_results), 1):
            print(f"\n  图片 {i}: {img_data['name']}")
            print(f"    文件存在: {'✅' if result.file_exists else '❌'}")
            print(f"    期望尺寸: {result.expected_width}x{result.expected_height}")
            print(f"    实际尺寸: {result.actual_width}x{result.actual_height}")
            print(f"    尺寸匹配: {'✅' if result.size_match else '❌'}")
            if result.errors:
                print(f"    错误: {', '.join(result.errors)}")
        
        # 测试自动修正
        print(f"\n📋 自动修正测试:")
        corrected_data = validator.correct_image_sizes(test_images)
        
        correction_count = 0
        for i, (original, corrected) in enumerate(zip(test_images, corrected_data), 1):
            if corrected.get('size_corrected', False):
                correction_count += 1
                print(f"  图片 {i}: 尺寸已修正")
                print(f"    原始: {original['width']}x{original['height']}")
                print(f"    修正: {corrected['width']}x{corrected['height']}")
        
        # 获取统计信息
        stats = validator.get_validation_statistics()
        print(f"\n📊 验证统计:")
        print(f"  验证数量: {stats['validated_count']}")
        print(f"  匹配数量: {stats['matched_count']}")
        print(f"  匹配率: {stats['match_rate']:.1f}%")
        print(f"  文件存在率: {stats['file_exists_rate']:.1f}%")
        print(f"  自动修正数量: {correction_count}")
        
        # 清理临时文件
        for img_data in test_images[:3]:  # 只清理我们创建的文件
            try:
                os.remove(img_data['path'])
            except:
                pass
        os.rmdir(temp_dir)
        
        return stats['validated_count'] > 0
        
    except Exception as e:
        print(f"❌ 图片尺寸验证器测试失败: {str(e)}")
        return False

def test_test_vs_production_consistency():
    """测试测试模式vs正式环境一致性"""
    print("\n🧪 测试测试模式vs正式环境一致性")
    print("=" * 80)
    
    try:
        from core.coordinate_precision_unifier import (
            CoordinatePrecisionUnifier,
            TestModeCoordinateProcessor,
            ProductionModeCoordinateProcessor,
            CoordinateConfig
        )
        from core.image_size_validator import (
            ImageSizeValidator,
            TestModeImageSizeProcessor,
            ProductionModeImageSizeProcessor,
            ValidationConfig
        )
        
        # 创建统一的配置
        coord_config = CoordinateConfig()
        size_config = ValidationConfig()
        
        # 创建处理器
        coord_unifier = CoordinatePrecisionUnifier(coord_config)
        size_validator = ImageSizeValidator(size_config)
        
        test_coord_processor = TestModeCoordinateProcessor(coord_unifier)
        prod_coord_processor = ProductionModeCoordinateProcessor(coord_unifier)
        
        test_size_processor = TestModeImageSizeProcessor(size_validator)
        prod_size_processor = ProductionModeImageSizeProcessor(size_validator)
        
        # 测试数据
        test_images = [
            {'name': 'img1.jpg', 'path': '/test/img1.jpg', 'x': 10.7, 'y': 20.3, 'width': 120.5, 'height': 80.2},
            {'name': 'img2.jpg', 'path': '/test/img2.jpg', 'x': 122.1, 'y': 0.8, 'width': 80.9, 'height': 60.1},
            {'name': 'img3.jpg', 'path': '/test/img3.jpg', 'x': 0.2, 'y': 82.6, 'width': 100.3, 'height': 70.7}
        ]
        
        print(f"📋 一致性测试:")
        print(f"  测试图片: {len(test_images)} 个")
        
        # 测试坐标处理一致性
        test_coord_results = test_coord_processor.process_for_test_mode(test_images, 205, 5000)
        prod_coord_results = prod_coord_processor.process_for_production_mode(test_images, 205, 5000)
        
        coord_consistency = True
        for i, (test_result, prod_result) in enumerate(zip(test_coord_results, prod_coord_results), 1):
            test_coords = (test_result['x'], test_result['y'], test_result['width'], test_result['height'])
            prod_coords = (prod_result['x'], prod_result['y'], prod_result['width'], prod_result['height'])
            
            coords_match = test_coords == prod_coords
            
            print(f"  图片 {i} 坐标一致性: {'✅' if coords_match else '❌'}")
            print(f"    测试模式: {test_coords}")
            print(f"    正式环境: {prod_coords}")
            
            if not coords_match:
                coord_consistency = False
        
        # 测试尺寸处理一致性（使用模拟数据）
        size_test_results = test_size_processor.process_for_test_mode(test_images)
        size_prod_results = prod_size_processor.process_for_production_mode(test_images)
        
        size_consistency = True
        for i, (test_result, prod_result) in enumerate(zip(size_test_results, size_prod_results), 1):
            # 比较处理逻辑是否一致（由于文件不存在，主要检查错误处理）
            test_validation = test_result.get('size_validation', {})
            prod_validation = prod_result.get('size_validation', {})
            
            # 检查验证结果结构是否一致
            validation_consistent = (
                test_validation.get('file_exists') == prod_validation.get('file_exists') and
                test_validation.get('size_match') == prod_validation.get('size_match')
            )
            
            print(f"  图片 {i} 尺寸验证一致性: {'✅' if validation_consistent else '❌'}")
            
            if not validation_consistent:
                size_consistency = False
        
        overall_consistency = coord_consistency and size_consistency
        
        print(f"\n📊 一致性测试结果:")
        print(f"  坐标处理一致性: {'✅ 通过' if coord_consistency else '❌ 失败'}")
        print(f"  尺寸验证一致性: {'✅ 通过' if size_consistency else '❌ 失败'}")
        print(f"  总体一致性: {'✅ 通过' if overall_consistency else '❌ 失败'}")
        
        return overall_consistency
        
    except Exception as e:
        print(f"❌ 一致性测试失败: {str(e)}")
        return False

def test_utilization_improvement_simulation():
    """测试利用率改善模拟"""
    print("\n🧪 测试利用率改善模拟")
    print("=" * 80)
    
    try:
        from core.coordinate_precision_unifier import CoordinatePrecisionUnifier, CoordinateConfig
        from core.image_size_validator import ImageSizeValidator, ValidationConfig
        
        # 模拟优化前后的数据
        canvas_width, canvas_height = 205, 5000
        
        # 原始数据（有精度问题）
        original_images = [
            {'name': 'img1', 'x': 0.7, 'y': 0.3, 'width': 120.2, 'height': 80.1},
            {'name': 'img2', 'x': 122.1, 'y': 0.8, 'width': 80.9, 'height': 60.2},
            {'name': 'img3', 'x': 0.2, 'y': 82.6, 'width': 100.3, 'height': 70.7},
            {'name': 'img4', 'x': 102.5, 'y': 82.1, 'width': 90.8, 'height': 50.9}
        ]
        
        print(f"📋 利用率改善模拟:")
        
        # 计算原始利用率（模拟测试模式）
        original_area = sum(img['width'] * img['height'] for img in original_images)
        original_utilization = (original_area / (canvas_width * canvas_height)) * 100
        
        print(f"  原始数据:")
        print(f"    图片数量: {len(original_images)}")
        print(f"    总面积: {original_area:.1f} 像素²")
        print(f"    利用率: {original_utilization:.3f}%")
        
        # 使用优化后的处理器
        coord_unifier = CoordinatePrecisionUnifier(CoordinateConfig())
        size_validator = ImageSizeValidator(ValidationConfig())
        
        # 处理坐标精度
        optimized_images = coord_unifier.batch_process_coordinates(original_images, canvas_width, canvas_height)
        
        # 计算优化后利用率
        optimized_area = sum(img['width'] * img['height'] for img in optimized_images)
        optimized_utilization = (optimized_area / (canvas_width * canvas_height)) * 100
        
        print(f"  优化后数据:")
        print(f"    图片数量: {len(optimized_images)}")
        print(f"    总面积: {optimized_area:.1f} 像素²")
        print(f"    利用率: {optimized_utilization:.3f}%")
        
        # 计算改善效果
        area_difference = optimized_area - original_area
        utilization_difference = optimized_utilization - original_utilization
        
        print(f"  改善效果:")
        print(f"    面积差异: {area_difference:.1f} 像素²")
        print(f"    利用率差异: {utilization_difference:.3f}%")
        print(f"    改善方向: {'✅ 提升' if utilization_difference >= 0 else '⚠️ 下降'}")
        
        # 分析精度改善
        coord_stats = coord_unifier.get_processing_statistics()
        print(f"  精度改善:")
        print(f"    处理成功率: {coord_stats['success_rate']:.1f}%")
        print(f"    平均X坐标变化: {coord_stats['precision_changes']['x_avg_change']:.2f} 像素")
        print(f"    平均Y坐标变化: {coord_stats['precision_changes']['y_avg_change']:.2f} 像素")
        
        return True
        
    except Exception as e:
        print(f"❌ 利用率改善模拟失败: {str(e)}")
        return False

def generate_phase3_optimization_test_report():
    """生成第三阶段优化测试报告"""
    print("\n📊 第三阶段优化测试报告")
    print("=" * 80)
    
    # 执行所有测试
    test_results = {
        'coordinate_precision': test_coordinate_precision_unifier(),
        'image_size_validation': test_image_size_validator(),
        'consistency_test': test_test_vs_production_consistency(),
        'utilization_simulation': test_utilization_improvement_simulation()
    }
    
    # 统计结果
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    print(f"\n📋 测试结果汇总:")
    print(f"  坐标精度统一: {'✅ 通过' if test_results['coordinate_precision'] else '❌ 失败'}")
    print(f"  图片尺寸验证: {'✅ 通过' if test_results['image_size_validation'] else '❌ 失败'}")
    print(f"  一致性测试: {'✅ 通过' if test_results['consistency_test'] else '❌ 失败'}")
    print(f"  利用率模拟: {'✅ 通过' if test_results['utilization_simulation'] else '❌ 失败'}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print(f"🎉 第三阶段优化测试全部通过！")
        print(f"✅ 测试vs正式环境一致性问题已解决")
        
        # 优化效果总结
        print(f"\n🎯 优化效果总结:")
        print(f"  1. ✅ 坐标精度统一: 消除浮点数误差，确保整数像素精度")
        print(f"  2. ✅ 图片尺寸预验证: 提前发现尺寸不匹配，支持自动修正")
        print(f"  3. ✅ 处理逻辑一致: 测试和正式环境使用完全相同的处理逻辑")
        print(f"  4. ✅ 利用率改善: 通过精度优化提升空间利用效率")
        
    else:
        print(f"⚠️ 第三阶段优化测试存在问题，需要修复")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🧪 第三阶段优化测试：测试vs正式环境一致性")
    print("=" * 100)
    print("测试目标:")
    print("1. 🔍 验证坐标精度统一器功能")
    print("2. 🔍 验证图片尺寸预验证器功能")
    print("3. 🔍 验证测试模式与正式环境的一致性")
    print("4. 🔍 模拟利用率改善效果")
    print("=" * 100)
    
    # 执行测试
    success = generate_phase3_optimization_test_report()
    
    if success:
        print(f"\n🎯 下一步行动:")
        print(f"1. ✅ 第三阶段优化已完成并验证")
        print(f"2. 🚀 可以开始第四阶段：综合集成和部署")
        print(f"3. 🔧 在实际环境中测试利用率改善效果")
        print(f"4. 📊 监控测试vs正式环境的一致性")
    else:
        print(f"\n🔧 需要修复的问题:")
        print(f"1. 检查失败的优化测试")
        print(f"2. 修复相关功能")
        print(f"3. 重新运行测试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
